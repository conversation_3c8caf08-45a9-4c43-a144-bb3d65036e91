"use strict";const e=require("../../../common/vendor.js");Math||a();const a=()=>"../common/CustomModal.js",t="踏入新征程，开启新篇章，这里是你梦想的起点！在这里，你将收获知识的力量，结识志同道合的朋友，探索无限可能的未来。河南农业大学将为你提供广阔的学习平台，丰富的实践机会，优秀的师资团队，完善的校园设施。无论你来自哪里，无论你的专业是什么，这里都将成为你人生中最重要的成长阶段。让我们一起在这片充满希望的土地上，书写属于你的青春华章，追求学术的真理，培养创新的思维，锻炼实践的能力，成就更好的自己！愿你在这里度过充实而美好的大学时光，收获知识、友谊和成长！",n={__name:"index",setup(a){const n=e.ref([{src:"https://itstudio.henau.edu.cn/image_hosting/uploads/68931460c8aeb_1754469472.png",alt:"校园风光"},{src:"https://itstudio.henau.edu.cn/image_hosting/uploads/6893147e19cdd_1754469502.png",alt:"新生报到"},{src:"https://itstudio.henau.edu.cn/image_hosting/uploads/6893149faf7f0_1754469535.png",alt:"教学活动"}]),o=e.ref(""),s=e.ref(!1),l=e.ref(null);e.ref(!1),e.ref([]);const u=e.ref({summary:{totalVisits:0}}),i=e=>{};e.onShareAppMessage((()=>({title:"河南农业大学新生指南中心",path:"/pages/index/index",imageUrl:n.value[0].src})));const r=e.ref(!1),c=e.ref(""),d=()=>{const e=u.value.summary?`\n\n至今访问量已有: ${u.value.summary.totalVisits}`:"\n\n访问量数据加载中...";c.value='河南农业大学IT工作室隶属于河南农业大学信息化办公室，主要负责校园信息化建设与维护。\n-----------主要职务内容-------------\n前端开发(Web/小程序)\n后端开发\nUI/UX设计\n运维技术\n-----------------------------------\n有意向的同学请关注\n"河南农业大学信息化办公室"公众号\n我们期待你的加入！'+e,r.value=!0},v=()=>{r.value=!1},g=()=>{s.value||null===l.value||(clearInterval(l.value),o.value=t,s.value=!0,l.value=null)},h=()=>{e.index.navigateTo({url:"/pages/index/index"})};return e.onLoad((()=>{console.log("页面加载"),(()=>{let e=0;l.value&&clearInterval(l.value),l.value=setInterval((()=>{e<t.length?(o.value+=t[e],e++):(clearInterval(l.value),s.value=!0,l.value=null)}),25)})(),e.index.login({provider:"weixin",success(a){a.code?e.index.request({url:"http://localhost:3000/api/auth/login",method:"POST",data:{code:a.code},success(e){console.log("登录成功！",e.data)},fail(e){console.error("登录请求失败：",e)}}):console.log("uni.login 失败！"+a.errMsg)},fail(e){console.error("uni.login 调用失败：",e)}}),e.index.request({url:"http://localhost:3000/api/users/statistics",method:"GET",success(a){200===a.statusCode&&"success"===a.data.status?(u.value=a.data.data,console.log("用户统计数据：",u.value)):(console.error("获取用户统计数据失败：",a.data.message||"未知错误"),e.index.showToast({title:"获取统计数据失败",icon:"none",duration:2e3}))},fail(a){console.error("请求用户统计数据失败：",a),e.index.showToast({title:"网络错误，无法获取统计数据",icon:"none",duration:2e3})}})})),(a,t)=>e.e({a:e.o(h),b:e.f(n.value,((a,t,o)=>({a:a.src,b:e.o((e=>(e=>{n.value[e].src="https://itstudio.henau.edu.cn/image_hosting/uploads/689314c7a5ee4_1754469575.png"})(t)),t),c:t}))),c:e.o(i),d:e.t(o.value),e:s.value},s.value?{f:e.o(d),g:e.o(v),h:e.p({visible:r.value,title:"河南农业大学IT工作室",content:c.value,confirmText:"好的",showCancel:!1})}:{},{i:e.o(g)})},__runtimeHooks:2};wx.createPage(n);
