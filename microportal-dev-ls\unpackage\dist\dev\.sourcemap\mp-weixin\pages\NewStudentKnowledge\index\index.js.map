{"version": 3, "file": "index.js", "sources": ["pages/NewStudentKnowledge/index/index.vue", "../../test/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvTmV3U3R1ZGVudEtub3dsZWRnZS9pbmRleC9pbmRleC52dWU"], "sourcesContent": ["<template>\n  <view class=\"page-container\" @tap=\"handlePageTap\">\n\n    <view class=\"top\">\n      <text class=\"top-title\">河南农业大学</text>\n      <text class=\"top-subtitle\">新生指南中心</text>\n    </view>\n\n    <!-- 返回按钮 -->\n    <view class=\"back-button\" @tap.stop=\"goBack\">\n      <!-- 使用 image 标签来显示图标 -->\n      <image class=\"back-icon-img\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/68960239b210b_1754661433.png\" alt=\"返回\"></image>\n    </view>\n\n    <!-- 轮播图组件 -->\n    <swiper\n      :indicator-dots=\"true\"\n      :autoplay=\"true\"\n      :interval=\"3000\"\n      :duration=\"1000\"\n      :circular=\"true\"\n      indicator-active-color=\"#07c160\"\n      class=\"banner-swiper\"\n      @change=\"handleSwiperChange\"\n    >\n      <swiper-item class=\"swiper-item\" v-for=\"(item, index) in bannerList\" :key=\"index\">\n        <image\n          class=\"banner-image\"\n          :src=\"item.src\"\n          mode=\"aspectFill\"\n          @error=\"handleImageError(index)\"\n        ></image>\n      </swiper-item>\n    </swiper>\n\n    <!-- 引言部分 -->\n    <view class=\"intro-box\">\n      <text class=\"intro-title\">引言</text>\n      <view class=\"intro-content\">\n        <text class=\"intro-text\">{{ introText }}</text>\n        <!-- 打字机效果光标 -->\n        <text v-if=\"!introTypingDone\" class=\"typing-cursor\">|</text>\n      </view>\n    </view>\n\n    <!-- 烟花特效蒙版 -->\n    <!-- 烟花特效蒙版是一个额外的视觉元素，通常在特定交互后触发，例如点击某个按钮 -->\n    <view v-if=\"showFirework\" class=\"firework-overlay\" @tap=\"hideFirework\">\n      <view class=\"firework-container\">\n        <view\n          v-for=\"(firework, index) in fireworks\"\n          :key=\"index\"\n          class=\"firework-particle\"\n          :style=\"firework.style\"\n        ></view>\n      </view>\n      <text class=\"firework-text\">🎉 欢迎开启新的人生篇章！🎉</text>\n    </view>\n\n    <!-- 统计信息弹窗 -->\n    <!-- 这个弹窗用于显示用户访问统计数据，当点击引言部分时会尝试拉取并显示 -->\n    <view v-if=\"showStatsModal\" class=\"stats-modal-overlay\" @tap=\"showStatsModal = false\">\n      <view class=\"stats-modal-content\" @tap.stop>\n        <text class=\"modal-title\">用户访问统计</text>\n        <!-- 安全地访问 statsData.summary.totalVisits，确保在数据未加载时显示“加载中” -->\n        <view v-if=\"statsData.summary\" class=\"stats-section\">\n          <text class=\"stats-item\">总访问量: {{ statsData.summary.totalVisits }}</text>\n        </view>\n        <text v-else class=\"stats-item\">加载统计数据中...</text>\n        <text class=\"close-modal-btn\" @tap=\"showStatsModal = false\">关闭</text>\n      </view>\n    </view>\n\n    <!-- 技术支持部分和访问量，只有在打字机效果完成后才显示 -->\n    <view class=\"bottom\" v-if=\"introTypingDone\">\n      <!-- 触发按钮，点击打开 IT 工作室招新模态框 -->\n      <text class=\"open-btn bottom-text\" @click=\"openModal\">河南农业大学IT工作室(欢迎加入)</text>\n      <!-- 引入自定义模态框组件，用于显示招新信息 -->\n      <CustomModal\n        :visible=\"isModalShow\"\n        title=\"河南农业大学IT工作室\"\n        :content=\"modalContent\"\n        confirmText=\"好的\"\n        :showCancel=\"false\"\n        @confirm=\"closeModal\"\n      />\n      <!-- 安全地显示总访问量，只有当 statsData.summary 存在时才显示 -->\n      <text class=\"bottom-text\" v-if=\"statsData.summary\"> | 总访问量: {{ statsData.summary.totalVisits }}</text>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref } from 'vue';\nimport { onLoad, onShow, onShareAppMessage } from '@dcloudio/uni-app';\n\n// 后端 API 的基础 URL\n// 注意：在生产环境中，请将 'http://localhost' 替换为您的实际部署地址\nconst backUrl = 'http://localhost';\n\n// 轮播图数据，包含图片地址和 alt 文本\nconst bannerList = ref([\n  { src: 'https://itstudio.henau.edu.cn/image_hosting/uploads/68931460c8aeb_1754469472.png', alt: '校园风光' },\n  { src: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6893147e19cdd_1754469502.png', alt: '新生报到' },\n  { src: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6893149faf7f0_1754469535.png', alt: '教学活动' },\n]);\n\n// 引言打字机效果相关数据\nconst introText = ref(''); // 当前显示的引言文本\nconst fullIntroText = '踏入新征程，开启新篇章，这里是你梦想的起点！在这里，你将收获知识的力量，结识志同道合的朋友，探索无限可能的未来。河南农业大学将为你提供广阔的学习平台，丰富的实践机会，优秀的师资团队，完善的校园设施。无论你来自哪里，无论你的专业是什么，这里都将成为你人生中最重要的成长阶段。让我们一起在这片充满希望的土地上，书写属于你的青春华章，追求学术的真理，培养创新的思维，锻炼实践的能力，成就更好的自己！愿你在这里度过充实而美好的大学时光，收获知识、友谊和成长！';\nconst introTypingDone = ref(false); // 打字机效果是否完成\nconst introIntervalId = ref(null); // 用于存储打字机效果的定时器ID\n\n// 烟花特效相关数据 (此部分代码在此次修复中未变动，但保留以供完整性)\nconst showFirework = ref(false);\nconst fireworks = ref([]);\n\n// 统计信息弹窗相关数据\nconst showStatsModal = ref(false);\n// 修复：初始化 statsData，确保 summary 及其 totalVisits 属性始终存在，避免 TypeError\nconst statsData = ref({ summary: { totalVisits: 0 } });\nconst introClickCount = ref(0); // 用于跟踪引言部分的点击次数\n\n// 开始引言打字机动画\nconst startIntroTyping = () => {\n  let i = 0;\n  // 清除任何可能存在的旧定时器，防止重复执行\n  if (introIntervalId.value) {\n    clearInterval(introIntervalId.value);\n  }\n  introIntervalId.value = setInterval(() => {\n    if (i < fullIntroText.length) {\n      introText.value += fullIntroText[i];\n      i++;\n    } else {\n      clearInterval(introIntervalId.value);\n      introTypingDone.value = true;\n      introIntervalId.value = null; // 动画完成后清除ID\n    }\n  }, 25);\n};\n\n// 轮播图切换事件处理\nconst handleSwiperChange = (e) => {\n  // console.log('当前轮播索引：', e.detail.current);\n};\n\n// 图片加载错误处理，显示默认图片\nconst handleImageError = (index) => {\n  bannerList.value[index].src = 'https://itstudio.henau.edu.cn/image_hosting/uploads/689314c7a5ee4_1754469575.png'; // 替换为默认图片\n};\n\n// 微信小程序分享配置\n// 当用户点击右上角菜单中的“转发”按钮时，会调用此函数\nonShareAppMessage(() => {\n  return {\n    title: '河南农业大学新生指南中心', // 分享卡的标题\n    path: '/pages/index/index', // 分享后用户点击进入的页面路径，请确保路径正确\n    imageUrl: bannerList.value[0].src, // 分享卡上显示的图片，这里使用第一张轮播图\n  };\n});\n\n// 导入自定义模态框组件\n// 请确保这里的路径与您的 CustomModal.vue 文件实际路径相符\nimport CustomModal from '../common/CustomModal.vue';\n\nconst isModalShow = ref(false); // 控制招新模态框的显示与隐藏\n// 招新模态框的内容，直接使用 \\n 进行换行\nconst modalContent = ref(`河南农业大学IT工作室隶属于学校信息化办公室，主要负责校园信息化建设与维护。\n\n招募方向：\n- 前端开发(Web/小程序)\n- 后端开发\n- UI/UX设计\n- 运维技术\n\n有意向的同学请关注\"河南农业大学信息化办公室\"公众号了解详情！`);\n\n// 打开招新模态框\nconst openModal = () => {\n  isModalShow.value = true;\n};\n\n// 关闭招新模态框（确认按钮回调）\nconst closeModal = () => {\n  isModalShow.value = false;\n  // 这里可写确认后的逻辑，比如埋点、跳转等\n};\n\n\n// 处理小程序登录逻辑\nconst handleLogin = () => {\n  uni.login({\n    provider: 'weixin', // 指定微信登录\n    success(res) {\n      if (res.code) {\n        // 发送 code 到后端服务器进行验证和登录\n        uni.request({\n          url: `${backUrl}:3000/api/auth/login`, // 您的后端登录接口\n          method: 'POST',\n          data: {\n            code: res.code,\n          },\n          success(res) {\n            console.log('登录成功！', res.data);\n          },\n          fail(err) {\n            console.error('登录请求失败：', err);\n          }\n        });\n      } else {\n        console.log('uni.login 失败！' + res.errMsg);\n      }\n    },\n    fail(err) {\n      // 提示：如果在非小程序环境（如浏览器）运行 uni-app 项目，\n      // uni.login 可能会因为环境不支持而报错。这通常不是代码逻辑问题。\n      console.error('uni.login 调用失败：', err);\n    }\n  });\n};\n\n// 获取用户访问统计数据\nconst fetchUserStatistics = () => {\n  uni.request({\n    url: `${backUrl}:3000/api/users/statistics`,\n    method: 'GET', // 根据API文档，这里应该是GET请求\n    success(res) {\n      if (res.statusCode === 200 && res.data.status === 'success') {\n        statsData.value = res.data.data; // 更新统计数据\n        showStatsModal.value = true; // 显示统计弹窗\n        console.log('用户统计数据：', statsData.value);\n      } else {\n        console.error('获取用户统计数据失败：', res.data.message || '未知错误');\n        uni.showToast({\n          title: '获取统计数据失败',\n          icon: 'none',\n          duration: 2000\n        });\n      }\n    },\n    fail(err) {\n      console.error('请求用户统计数据失败：', err);\n      uni.showToast({\n        title: '网络错误，无法获取统计数据',\n        icon: 'none',\n        duration: 2000\n      });\n    }\n  });\n};\n\n// 处理引言部分点击事件 (用于触发统计弹窗)\nconst handleIntroClick = () => {\n  // 当打字机效果完成后，点击引言部分将尝试获取并显示统计数据\n  if (introTypingDone.value) {\n    introClickCount.value++;\n    // 可以设置一个点击阈值，例如点击3次才显示统计，这里为每次点击都触发\n    if (introClickCount.value >= 1) {\n      fetchUserStatistics();\n      // introClickCount.value = 0; // 如果希望每次显示都重置计数器，则取消注释\n    }\n  }\n};\n\n\n// 处理页面全局点击事件 (用于停止打字机效果)\nconst handlePageTap = () => {\n  // 如果打字机效果仍在进行中且定时器存在\n  if (!introTypingDone.value && introIntervalId.value !== null) {\n    clearInterval(introIntervalId.value); // 停止打字计时器\n    introText.value = fullIntroText;      // 立即显示完整文本\n    introTypingDone.value = true;         // 标记打字机效果完成\n    introIntervalId.value = null;         // 清空定时器ID\n  }\n};\n\n// 返回上一页\nconst goBack = () => {\n  // 这里使用 navigateTo 返回首页，如果想返回上一级页面，可以使用 uni.navigateBack()\n  uni.navigateTo({\n    url:'/pages/index/index'\n  })\n};\n\n// 页面加载时执行\nonLoad(() => {\n  console.log('页面加载');\n  // 页面加载时开始引言打字机动画\n  startIntroTyping();\n  // 页面加载时执行登录请求\n  handleLogin();\n});\n\n// 页面显示时执行\nonShow(() => {\n  console.log('页面显示');\n});\n</script>\n\n<style lang=\"scss\">\n@charset \"UTF-8\";\n/* 引入外部公共样式，如果需要的话 */\n/* @import '@/common/style.scss'; */\n\n/* 页面容器基础样式 */\n.page-container {\n  min-height: 100vh; /* 确保页面至少占满视口高度 */\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 50%, #fff0f5 100%); /* 渐变背景色 */\n  padding: 15rpx;\n  /* 适配安全区域 */\n  padding-top: calc(15rpx + env(safe-area-inset-top));\n  padding-bottom: calc(15rpx + env(safe-area-inset-bottom));\n  box-sizing: border-box; /* 盒模型为边框盒 */\n  position: relative;\n  font-family: 'Inter', sans-serif; /* 确保使用 Inter 字体 */\n}\n\n/* 返回按钮样式 */\n.back-button {\n  position: absolute;\n  top: calc(55rpx + env(safe-area-inset-top));\n  left: 30rpx;\n  width: 80rpx;\n  height: 80rpx;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);\n  z-index: 1000;\n  transition: all 0.3s ease;\n  border: 1rpx solid rgba(255, 255, 255, 0.3);\n  overflow: hidden; /* 确保图片不会溢出圆形按钮 */\n}\n\n.back-button:active {\n  transform: scale(0.9);\n  background: rgba(240, 240, 240, 0.9);\n}\n\n.back-icon-img {\n  width: 70rpx; /* 调整尺寸以获得更好的视觉效果 */\n  height: 70rpx; /* 调整尺寸以获得更好的视觉效果 */\n  object-fit: contain; /* 确保图片在按钮内缩放而不裁剪 */\n  display: block; /* 移除行内元素下方的额外空间 */\n}\n\n/* 底部文字样式 */\n.bottom {\n  font-size: 28rpx; /* 调整字体大小 */\n  font-weight: 400; /* 调整字重 */\n  text-align: center;\n  color: #7a776f;\n  margin-top: 40rpx; /* 增加顶部外边距 */\n  display: flex; /* 使用 flex 布局 */\n  flex-direction: column; /* 垂直排列 */\n  gap: 10rpx; /* 增加行间距 */\n  padding: 20rpx;\n}\n\n.bottom-text {\n  background: rgba(255, 255, 255, 0.8);\n  border-radius: 15rpx;\n  padding: 15rpx 30rpx;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n  transition: all 0.3s ease;\n  &:active {\n    transform: scale(0.98);\n  }\n}\n\n/* 顶部标题区域样式 */\n.top {\n  text-align: center;\n  font-size: 38rpx;\n  font-weight: 500;\n  color: #2d3748;\n  margin-bottom: 20rpx;\n  padding: 30rpx 25rpx;\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 25rpx;\n  backdrop-filter: blur(20rpx); /* 磨砂玻璃效果 */\n  border: none;\n  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);\n  position: relative;\n  overflow: hidden;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;\n  letter-spacing: 2rpx;\n}\n\n/* 顶部标题闪烁效果 */\n.top::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);\n  animation: shimmer 3s infinite;\n}\n\n.top-title {\n  display: block;\n  font-size: 42rpx;\n  font-weight: 700;\n  color: #2d3748;\n  margin-bottom: 8rpx;\n  position: relative;\n  z-index: 1;\n}\n\n.top-subtitle {\n  display: block;\n  font-size: 28rpx;\n  font-weight: 400;\n  color: #718096;\n  position: relative;\n  z-index: 1;\n}\n\n/* 轮播图样式 */\n.banner-swiper {\n  height: 420rpx;\n  width: 92%;\n  max-width: 700rpx;\n  margin: 0 auto;\n  border-radius: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 12rpx 35rpx rgba(0, 0, 0, 0.12);\n  border: 1rpx solid rgba(255, 255, 255, 0.3);\n}\n\n.swiper-item {\n  height: 100%;\n}\n\n.banner-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* 引言部分样式 */\n.intro-box {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 40rpx 30rpx;\n  min-height: 180rpx;\n  background: transparent;\n  margin-top: 20rpx;\n  position: relative;\n}\n\n.intro-title {\n  font-size: 52rpx;\n  font-weight: 700;\n  color: #4a5568;\n  margin-bottom: 35rpx;\n  letter-spacing: 4rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n  text-align: center;\n}\n\n.intro-content {\n  display: flex;\n  align-items: flex-start;\n  justify-content: flex-start;\n  width: 100%;\n}\n\n.intro-text {\n  font-size: 32rpx;\n  color: #2d3748;\n  white-space: pre-wrap;\n  word-break: break-all;\n  font-weight: 400;\n  text-align: left;\n  line-height: 1.8;\n  position: relative;\n  z-index: 1;\n  letter-spacing: 1rpx;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;\n  flex: 1;\n  display: inline-block;\n}\n\n/* 打字机效果光标 */\n.typing-cursor {\n  font-weight: bold;\n  color: #667eea;\n  margin-left: 5rpx;\n  animation: blink 1s infinite step-end;\n  font-size: 36rpx;\n  line-height: 1.8;\n  align-self: flex-start;\n}\n\n/* 动画定义 */\n@keyframes fadeIn {\n  from { opacity: 0; }\n  to { opacity: 1; }\n}\n\n@keyframes slideUp {\n  from { transform: translateY(30rpx); opacity: 0; }\n  to { transform: translateY(0); opacity: 1; }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(1);\n    box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.4);\n  }\n  50% {\n    transform: scale(1.05);\n    box-shadow: 0 12rpx 30rpx rgba(102, 126, 234, 0.6);\n  }\n}\n\n@keyframes shimmer {\n  0% { transform: translateX(-100%); }\n  100% { transform: translateX(100%); }\n}\n\n@keyframes blink {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0; }\n}\n\n/* 烟花特效样式 */\n.firework-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  background: rgba(0, 0, 0, 0.8);\n  z-index: 9999;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  animation: fadeIn 0.3s ease-in-out;\n}\n\n.firework-container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n}\n\n.firework-particle {\n  position: absolute;\n  border-radius: 50%;\n  pointer-events: none;\n}\n\n.firework-text {\n  position: relative;\n  z-index: 10000;\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #fff;\n  text-align: center;\n  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.5);\n  animation: textGlow 1s ease-in-out infinite alternate;\n}\n\n@keyframes fireworkExplode {\n  0% {\n    transform: scale(0) rotate(0deg);\n    opacity: 1;\n  }\n  50% {\n    transform: scale(1.5) rotate(180deg);\n    opacity: 0.8;\n  }\n  100% {\n    transform: scale(3) rotate(360deg);\n    opacity: 0;\n  }\n}\n\n@keyframes textGlow {\n  0% {\n    text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.5), 0 0 20rpx rgba(255, 255, 255, 0.3);\n  }\n  100% {\n    text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.5), 0 0 40rpx rgba(255, 255, 255, 0.8);\n  }\n}\n\n/* 统计信息弹窗样式 */\n.stats-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100vw;\n  height: 100vh;\n  background: rgba(0, 0, 0, 0.6);\n  z-index: 9998;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  animation: fadeIn 0.3s ease-in-out;\n}\n\n.stats-modal-content {\n  background: #fff;\n  border-radius: 20rpx;\n  padding: 40rpx;\n  width: 80%;\n  max-width: 600rpx;\n  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  transform: scale(0.95);\n  opacity: 0;\n  animation: slideUp 0.3s forwards ease-out;\n}\n\n.modal-title {\n  font-size: 40rpx;\n  font-weight: bold;\n  color: #2d3748;\n  margin-bottom: 30rpx;\n  text-align: center;\n}\n\n.stats-section {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 15rpx;\n  margin-bottom: 30rpx;\n}\n\n.stats-item {\n  font-size: 30rpx;\n  color: #4a5568;\n  line-height: 1.5;\n}\n\n.close-modal-btn {\n  background-color: #07c160;\n  color: #fff;\n  padding: 15rpx 40rpx;\n  border-radius: 50rpx;\n  font-size: 32rpx;\n  font-weight: bold;\n  margin-top: 20rpx;\n  transition: all 0.3s ease;\n  box-shadow: 0 4rpx 10rpx rgba(7, 193, 96, 0.4);\n}\n\n.close-modal-btn:active {\n  transform: scale(0.95);\n  opacity: 0.8;\n}\n</style>\n", "import MiniProgramPage from 'D:/新生小程序/microportal-dev-ls/pages/NewStudentKnowledge/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onShareAppMessage", "uni", "res", "onLoad", "onShow", "MiniProgramPage"], "mappings": ";;;;;AAoKA,MAAA,cAAA,MAAA;AAlEA,MAAA,UAAA;AAWA,MAAA,gBAAA;;;;AARA,UAAA,aAAAA,cAAAA,IAAA;AAAA,MACA,EAAA,KAAA,oFAAA,KAAA,OAAA;AAAA,MACA,EAAA,KAAA,oFAAA,KAAA,OAAA;AAAA,MACA,EAAA,KAAA,oFAAA,KAAA,OAAA;AAAA,IACA,CAAA;AAGA,UAAA,YAAAA,cAAAA,IAAA,EAAA;AAEA,UAAA,kBAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,kBAAAA,cAAAA,IAAA,IAAA;AAGA,UAAA,eAAAA,cAAAA,IAAA,KAAA;AACA,UAAA,YAAAA,cAAAA,IAAA,CAAA,CAAA;AAGA,UAAA,iBAAAA,cAAAA,IAAA,KAAA;AAEA,UAAA,YAAAA,cAAA,IAAA,EAAA,SAAA,EAAA,aAAA,EAAA,EAAA,CAAA;AACAA,kBAAA,IAAA,CAAA;AAGA,UAAA,mBAAA,MAAA;AACA,UAAA,IAAA;AAEA,UAAA,gBAAA,OAAA;AACA,sBAAA,gBAAA,KAAA;AAAA,MACA;AACA,sBAAA,QAAA,YAAA,MAAA;AACA,YAAA,IAAA,cAAA,QAAA;AACA,oBAAA,SAAA,cAAA,CAAA;AACA;AAAA,QACA,OAAA;AACA,wBAAA,gBAAA,KAAA;AACA,0BAAA,QAAA;AACA,0BAAA,QAAA;AAAA,QACA;AAAA,MACA,GAAA,EAAA;AAAA,IACA;AAGA,UAAA,qBAAA,CAAA,MAAA;AAAA,IAEA;AAGA,UAAA,mBAAA,CAAA,UAAA;AACA,iBAAA,MAAA,KAAA,EAAA,MAAA;AAAA,IACA;AAIAC,kBAAAA,kBAAA,MAAA;AACA,aAAA;AAAA,QACA,OAAA;AAAA;AAAA,QACA,MAAA;AAAA;AAAA,QACA,UAAA,WAAA,MAAA,CAAA,EAAA;AAAA;AAAA,MACA;AAAA,IACA,CAAA;AAMA,UAAA,cAAAD,cAAAA,IAAA,KAAA;AAEA,UAAA,eAAAA,cAAA,IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCAQA;AAGA,UAAA,YAAA,MAAA;AACA,kBAAA,QAAA;AAAA,IACA;AAGA,UAAA,aAAA,MAAA;AACA,kBAAA,QAAA;AAAA,IAEA;AAIA,UAAA,cAAA,MAAA;AACAE,oBAAAA,MAAA,MAAA;AAAA,QACA,UAAA;AAAA;AAAA,QACA,QAAA,KAAA;AACA,cAAA,IAAA,MAAA;AAEAA,0BAAAA,MAAA,QAAA;AAAA,cACA,KAAA,GAAA,OAAA;AAAA;AAAA,cACA,QAAA;AAAA,cACA,MAAA;AAAA,gBACA,MAAA,IAAA;AAAA,cACA;AAAA,cACA,QAAAC,MAAA;AACAD,8BAAA,MAAA,MAAA,OAAA,oDAAA,SAAAC,KAAA,IAAA;AAAA,cACA;AAAA,cACA,KAAA,KAAA;AACAD,8BAAA,MAAA,MAAA,SAAA,oDAAA,WAAA,GAAA;AAAA,cACA;AAAA,YACA,CAAA;AAAA,UACA,OAAA;AACAA,0BAAA,MAAA,MAAA,OAAA,oDAAA,kBAAA,IAAA,MAAA;AAAA,UACA;AAAA,QACA;AAAA,QACA,KAAA,KAAA;AAGAA,wBAAA,MAAA,MAAA,SAAA,oDAAA,mBAAA,GAAA;AAAA,QACA;AAAA,MACA,CAAA;AAAA,IACA;AA+CA,UAAA,gBAAA,MAAA;AAEA,UAAA,CAAA,gBAAA,SAAA,gBAAA,UAAA,MAAA;AACA,sBAAA,gBAAA,KAAA;AACA,kBAAA,QAAA;AACA,wBAAA,QAAA;AACA,wBAAA,QAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,SAAA,MAAA;AAEAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGAE,kBAAAA,OAAA,MAAA;AACAF,oBAAAA,MAAA,MAAA,OAAA,oDAAA,MAAA;AAEA;AAEA;IACA,CAAA;AAGAG,kBAAAA,OAAA,MAAA;AACAH,oBAAAA,MAAA,MAAA,OAAA,oDAAA,MAAA;AAAA,IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxSA,GAAG,WAAWI,SAAe;"}