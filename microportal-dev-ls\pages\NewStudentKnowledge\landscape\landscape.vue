<template>
	<view class="title">
		左右滑动,点击查看大图
	</view>
	<swiper
		class="image-swiper"
		:indicator-dots="false"
		:autoplay="true"
		:interval="3000"
		:current="currentIndex"
		:circular="true"
	>
		<swiper-item
			v-for="(card, index) in originalCards"
			:key="index"
			@click="handleClick(index)"
		>
			<view class="swiper-item-content">
				<image class="card-image" :src="card.image" mode="aspectFit"></image>
				<view class="card-description-box">
					<text class="card-description">{{ card.description }}</text>
				</view>
			</view>
		</swiper-item>
	</swiper>

</template>

<script setup>
import { ref, onMounted } from 'vue';

// 原始图片数据
const originalCards = ref([
	{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6893031937e11_1754465049.jpg', description: '龙子湖' },
	{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/68930318e4fd0_1754465048.jpg', description: '龙子湖' },
	{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6893031859f14_1754465048.jpg', description: '龙子湖' },
	{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6893031709707_1754465047.jpg', description: '龙子湖' },
	{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/689302aa47ceb_1754464938.jpg', description: '文化路' },
	{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/689302aa43fd3_1754464938.jpg', description: '文化路' },
	{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6893026bcb788_1754464875.jpg', description: '桃李园' },
	{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6893026bb8318_1754464875.jpg', description: '桃李园' },
	{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6893026b97164_1754464875.jpg', description: '桃李园' },
	{ image: "https://itstudio.henau.edu.cn/image_hosting/uploads/689302aa255d8_1754464938.jpg", description: '文化路' },
	{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689302aa349a3_1754464938.jpg', description: '文化路' },
	{ image:"https://itstudio.henau.edu.cn/image_hosting/uploads/6893026bb09d8_1754464875.jpg", description: '桃李园' },
	{ image:"https://itstudio.henau.edu.cn/image_hosting/uploads/6893026b8c8e3_1754464875.jpg", description: '桃李园' },
	{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316ca6f20b_1754470090.png',description:'许昌校区'},
	{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316ca55caf_1754470090.png',description:'许昌校区'},
	{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316ca2ca47_1754470090.png',description:'许昌校区'},
	{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316c9f25af_1754470089.png',description:'许昌校区'},
	{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316ca0cc9a_1754470090.png',description:'许昌校区'},
	{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316ca217bb_1754470090.png',description:'许昌校区'},
	{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316c9e5ecc_1754470089.png',description:'许昌校区'},
	{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316c97bb8b_1754470089.png',description:'许昌校区'},
	{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316c93ac27_1754470089.png',description:'许昌校区'},
	{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316c94b785_1754470089.png',description:'许昌校区'},
	{ image:"https://itstudio.henau.edu.cn/image_hosting/uploads/689316c8cc4a4_1754470088.png" , description:"许昌校区"}
]);

const currentIndex = ref(0);

// Swiper 切换事件
const swiperChange = (e) => {
	currentIndex.value = e.detail.current;
};

// 处理卡片点击事件，跳转到详情页
const handleClick = (index) => {
	const clickedCard = originalCards.value[index];
	uni.navigateTo({
		url: `/pages/NewStudentKnowledge/detail/detail?imageUrl=${encodeURIComponent(clickedCard.image)}`
	});
};

onMounted(() => {
	// 页面加载时可以执行一些初始化操作，但此处不再需要复杂的动画逻辑
});
</script>

<style scoped>
.title {
	position: absolute;
	top: 100rpx; /* Adjusted top position */
	left: 50%;
	transform: translateX(-50%);
	font-size: 30rpx;
	font-weight: bold;
	color: #000000;
	text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
	z-index: 150;
}

.image-counter {
	position: absolute;
	top: 80rpx; /* 调整位置，避免与标题重叠 */
	left: 50%;
	transform: translateX(-50%);
	font-size: 24rpx;
	color: #ffffff;
	background-color: rgba(0, 0, 0, 0.5);
	padding: 8rpx 16rpx;
	border-radius: 16rpx;
	z-index: 150;
}

.image-swiper {
	width: 100vw;
	height: 95vh;
	/* 更新为渐变背景 */
	background: linear-gradient(135deg, #5dfd9d, #fbebf3, #fce1d1, #e68079);
	background-size: 400% 400%;
	animation: gradientAnimation 4s ease infinite;
}

@keyframes gradientAnimation {
	0% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0% 50%;
	}
}

.swiper-item-content {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
	border-radius: 20rpx; /* 给 swiper-item-content 添加圆角 */
	overflow: hidden; /* 裁剪超出圆角的部分 */
}

.card-image {
	width: 100%;
	height: 100%;
	object-fit: contain;
}

.card-description-box {
	border-radius: 10px;
	position: absolute;
	bottom: 20px;
	left: 50%;
	transform: translateX(-50%);
	width: 80%;
	padding: 20rpx;
	background-color: rgba(0, 0, 0, 0.5);
	color: #fff;
	font-size: 28rpx;
	text-align: center;
	box-sizing: border-box;
	z-index: 2;
}

.card-description {
	line-height: 2;
}

.bottom-text {
	position: fixed; /* 固定在视口底部 */
	bottom: -10px; /* Adjusted bottom position */
	left: 0;
	width: 100%;
	text-align: center;
	padding: 10rpx 0;
	color: #000000;
	font-size: 24rpx;
	z-index: 160; /* 确保在最上层 */
}
</style>
