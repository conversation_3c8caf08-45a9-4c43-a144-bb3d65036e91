{"name": "henau-mini-program-backend", "version": "1.0.0", "description": "河南农业大学小程序后端服务", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "mysql", "henau", "miniprogram"], "author": "", "license": "ISC", "dependencies": {"@google/generative-ai": "^0.24.1", "axios": "^1.6.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "mysql2": "^3.6.5", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}}