{"version": 3, "file": "microservices.js", "sources": ["pages/microservices/microservices.vue", "../../test/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbWljcm9zZXJ2aWNlcy9taWNyb3NlcnZpY2VzLnZ1ZQ"], "sourcesContent": ["<template>\n\t<!-- <web-view src=\"https://microservices.henau.edu.cn/henauwfw/#/index\"> -->\n\t<web-view src=\"https://microservice.leesong.top/henauwfw/#/index\">\n\t\t<cover-view class=\"close-view\" @click=\"closeView()\">\n\t\t\t<cover-image class=\"close-icon\" src=\"/static/icon/public/home.png\"></cover-image>\n\t\t</cover-view>\n\t</web-view>\n</template>\n\n<script setup>\n\timport {\n\t\tonShareAppMessage,\n\t\tonShareTimeline,\n\t\tonHide\n\t} from '@dcloudio/uni-app'\n\tconst closeView = () => {\n\t\tuni.reLaunch({\n\t\t\turl: '/pages/index/index'\n\t\t})\n\t}\n\t// 监听webview是否进后台，进后台则回到首页，清除页面栈\n\t// onHide(() => {\n\t// \tuni.reLaunch({\n\t// \t\turl: '/pages/index/index'\n\t// \t})\n\t// })\n\tonShareAppMessage((options) => {\n\t\t// console.log(options.webViewUrl)\n\t\t// 获取的是h5的真实地址\n\t\t// uni.showModal({\n\t\t// \ttitle: '有确认取消的弹窗',\n\t\t// \tcontent: options.webViewUrl,\n\t\t// \tsuccess: function(res) {\n\t\t// \t\tif (res.confirm) {\n\t\t// \t\t\tconsole.log('点击了确认')\n\t\t// \t\t} else {\n\t\t// \t\t\tconsole.log('点击了取消')\n\t\t// \t\t}\n\t\t// \t}\n\t\t// })\n\t\t// 获取 webViewUrl\n\t\tconst webViewUrl = options.webViewUrl;\n\t\t// 构建携带参数的路径\n\t\tconst sharePath = `/pages/index/index?webViewUrl=${encodeURIComponent(webViewUrl)}`;\n\t\treturn {\n\t\t\tpath: sharePath,\n\t\t}\n\t})\n\tonShareTimeline(() => {})\n</script>\n\n<style>\n\t.close-view {\n\t\tbackground-color: #616161;\n\t\tborder-radius: 50%;\n\t\tposition: fixed;\n\t\tz-index: 99999;\n\t\tbottom: 19vh;\n\t\tright: 30px;\n\t\tvisibility: visible !important;\n\t\tpadding: 5px;\n\t}\n\n\t.close-icon {\n\t\twidth: 30px;\n\t\theight: 30px;\n\t}\n</style>", "import MiniProgramPage from 'D:/新生小程序/microportal-dev-ls/pages/microservices/microservices.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "onShareAppMessage", "onShareTimeline", "MiniProgramPage"], "mappings": ";;;;;;AAUA,UAAA,YAAA,MAAA;AAMAA,oBAAAA,MAAA,SAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAOAC,kBAAA,kBAAA,CAAA,YAAA;AAeA,YAAA,aAAA,QAAA;AAEA,YAAA,YAAA,iCAAA,mBAAA,UAAA,CAAA;AACA,aAAA;AAAA,QACA,MAAA;AAAA,MACA;AAAA,IACA,CAAA;AACAC,kBAAA,gBAAA,MAAA;AAAA,IAAA,CAAA;;;;;;;;;;AC/CA,GAAG,WAAWC,SAAe;"}