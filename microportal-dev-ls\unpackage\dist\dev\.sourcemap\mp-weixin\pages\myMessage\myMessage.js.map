{"version": 3, "file": "myMessage.js", "sources": ["pages/myMessage/myMessage.vue", "../../test/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbXlNZXNzYWdlL215TWVzc2FnZS52dWU"], "sourcesContent": ["<template>\r\n\t<!-- 我的消息 -->\r\n\t<web-view src=\"https://microservices.henau.edu.cn/henauwfw/#/MyMessage\">\r\n\t\t<cover-view class=\"close-view\" @click=\"closeView()\">\r\n\t\t\t<cover-image class=\"close-icon\" src=\"/static/icon/public/home.png\"></cover-image>\r\n\t\t</cover-view>\r\n\t</web-view>\r\n</template>\r\n\r\n<script setup>\r\n\timport {\r\n\t\tonShareAppMessage,\r\n\t\tonShareTimeline\r\n\t} from '@dcloudio/uni-app'\r\n\tconst closeView = () => {\r\n\t\tuni.reLaunch({\r\n\t\t\turl: '/pages/index/index'\r\n\t\t})\r\n\t}\r\n\tonShareAppMessage((options) => {\r\n\t\t// 获取 webViewUrl\r\n\t\tconst webViewUrl = options.webViewUrl;\r\n\t\t// 构建携带参数的路径\r\n\t\tconst sharePath = `/pages/index/index?webViewUrl=${encodeURIComponent(webViewUrl)}`;\r\n\t\treturn {\r\n\t\t\tpath: sharePath,\r\n\t\t}\r\n\t})\r\n\tonShareTimeline(() => {})\r\n</script>\r\n\r\n<style>\r\n\t.close-view {\r\n\t\tbackground-color: #616161;\r\n\t\tborder-radius: 50%;\r\n\t\tposition: fixed;\r\n\t\tz-index: 99999;\r\n\t\tbottom: 19vh;\r\n\t\tright: 30px;\r\n\t\tvisibility: visible !important;\r\n\t\tpadding: 5px;\r\n\t}\r\n\r\n\t.close-icon {\r\n\t\twidth: 30px;\r\n\t\theight: 30px;\r\n\t}\r\n</style>", "import MiniProgramPage from 'D:/新生小程序/microportal-dev-ls/pages/myMessage/myMessage.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "onShareAppMessage", "onShareTimeline", "MiniProgramPage"], "mappings": ";;;;;;AAUA,UAAA,YAAA,MAAA;AAKAA,oBAAAA,MAAA,SAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AACAC,kBAAA,kBAAA,CAAA,YAAA;AAEA,YAAA,aAAA,QAAA;AAEA,YAAA,YAAA,iCAAA,mBAAA,UAAA,CAAA;AACA,aAAA;AAAA,QACA,MAAA;AAAA,MACA;AAAA,IACA,CAAA;AACAC,kBAAA,gBAAA,MAAA;AAAA,IAAA,CAAA;;;;;;;;;;AC3BA,GAAG,WAAWC,SAAe;"}