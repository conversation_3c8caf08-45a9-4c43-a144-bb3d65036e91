"use strict";const t=require("../../../common/vendor.js"),e={__name:"CustomModal",props:{visible:{type:Boolean,default:!1},title:{type:String,default:""},content:{type:String,default:""},confirmText:{type:String,default:"确认"},cancelText:{type:String,default:"取消"},showCancel:{type:Boolean,default:!1},confirmBtnColor:{type:String,default:"#4CAF50"},cancelBtnColor:{type:String,default:"#607D8B"},lineHeight:{type:Number,default:48},showButton:{type:Boolean,default:!0}},emits:["confirm","cancel"],setup(e,{emit:n}){const o=n,l=()=>{o("confirm")},c=()=>{o("cancel")};return(n,o)=>t.e({a:e.title},e.title?{b:t.t(e.title)}:{},{c:t.t(e.content),d:e.lineHeight+"rpx",e:e.showButton},e.showButton?t.e({f:e.showCancel},e.showCancel?{g:t.t(e.cancelText),h:e.cancelBtnColor,i:e.cancelBtnColor,j:t.o(c)}:{},{k:t.t(e.confirmText),l:e.confirmBtnColor,m:t.o(l)}):{},{n:e.visible?1:"",o:e.visible?1:""})}},n=t._export_sfc(e,[["__scopeId","data-v-b27a5c80"]]);wx.createComponent(n);
