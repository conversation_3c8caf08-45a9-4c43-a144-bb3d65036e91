"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Math) {
  CustomModal();
}
const CustomModal = () => "../common/CustomModal.js";
const _sfc_main = {
  __name: "test",
  setup(__props) {
    const isModalShow = common_vendor.ref(false);
    const modalContent = common_vendor.ref(`河南农业大学IT工作室隶属于学校信息化办公室，主要负责校园信息化建设与维护。

招募方向：
- 前端开发(Web/小程序)
- 后端开发
- UI/UX设计
- 运维技术

有意向的同学请关注"河南农业大学信息化办公室"公众号了解详情！`);
    const openModal = () => {
      isModalShow.value = true;
    };
    const closeModal = () => {
      isModalShow.value = false;
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(openModal),
        b: common_vendor.o(closeModal),
        c: common_vendor.p({
          visible: isModalShow.value,
          title: "河南农业大学IT工作室",
          content: modalContent.value,
          confirmText: "好的",
          showCancel: false
        })
      };
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/NewStudentKnowledge/test/test.js.map
