"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  __name: "ac",
  setup(__props) {
    const webviewUrl = common_vendor.ref("");
    common_vendor.onMounted(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options;
      if (options.webviewUrl) {
        webviewUrl.value = decodeURIComponent(options.webviewUrl);
      }
    });
    common_vendor.onHide(() => {
      common_vendor.index.reLaunch({
        url: "/pages/index/index"
      });
    });
    common_vendor.onUnload(() => {
    });
    common_vendor.onBackPress((options) => {
    });
    const closeView = () => {
      common_vendor.index.reLaunch({
        url: "/pages/index/index"
      });
    };
    common_vendor.onShareAppMessage((options) => {
      const webViewUrl = options.webViewUrl;
      const sharePath = `/pages/index/index?webViewUrl=${encodeURIComponent(
        webViewUrl
      )}`;
      return {
        path: sharePath
      };
    });
    common_vendor.onShareTimeline(() => {
    });
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0,
        b: common_vendor.o(($event) => closeView()),
        c: webviewUrl.value
      };
    };
  }
};
_sfc_main.__runtimeHooks = 6;
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/ac/ac.js.map
