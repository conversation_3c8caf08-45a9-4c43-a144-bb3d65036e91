
.container {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		/* 确保容器至少占满视口高度 */
}
.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 15px;
		margin-top: 5px;
		background-color: #f8f8f8;
}
.grid-container {
		width: 100%;
}
.grid-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 100%;
}
.grid-item-icon {
		width: 40px;
		height: 40px;
		margin-bottom: 8px;
		/* 图标和文本的间距 */
		object-fit: contain;
}
.grid-item-text {
		font-size: 12px;
		/* 字体大小统一设置为12px */
		color: #000;
		text-align: center;
}

	/* 最后一行不足四个时，自动向左对齐 */
.grid-container>.grid-item:nth-child(4n + 1) {
		/* 这里是为了避免最后一行空隙，保持左对齐 */
		margin-left: 0;
}
.service-container {
		/* 列与列、行与行之间的间距 */
		width: 100%;
		max-width: 1200px;
		/* 控制容器最大宽度 */
		padding: 15px 0;
		background-color: #fff;
		border-radius: 12px;
}
.service-list {
		/* width: 100%; */
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		/* 每行四列 */
		grid-gap: 10px;

		/* box-shadow: rgba(50, 50, 105, 0.15) 0px 2px 5px 0px, rgba(0, 0, 0, 0.05) 0px 1px 1px 0px; */
}
.service-title {
		color: #999999;
		font-size: 12px;
		margin: 0 0 15px 10px;
}
.copyright {
		text-align: center;
		margin-top: auto;
		margin-bottom: 30px;
		color: #999999;
}
.version-number {
		cursor: pointer;
		-webkit-user-select: none;
		        user-select: none;
		font-size: 12px;
		/* 禁止选中文本，有助于减少默认交互效果 */
		-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
		/* 将点击高亮颜色设置为透明，去除点击时的背景色显示 */
}
.technical-support {
		cursor: pointer;
		-webkit-user-select: none;
		        user-select: none;
		font-size: 12px;
		/* 禁止选中文本，有助于减少默认交互效果 */
		-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
		/* 将点击高亮颜色设置为透明，去除点击时的背景色显示 */
		margin-top: 5px;
}
.head-image {
		width: 100%;
}
.head-bg-img {
		width: 100%;
		height: 150px;
}
.widget-container {
		/* 列与列、行与行之间的间距 */
		width: 100%;
		max-width: 1200px;
		/* 控制容器最大宽度 */
		padding: 10px 0;
}
.widget-list {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 15px;
}
.widget-item {
		display: flex;
		align-items: center;
		text-align: justify;
		padding: 15px 10px;
		background-color: #fff;
		border-radius: 12px;
}
.icon-container {
		width: 40%;
		display: flex;
		align-items: center;
		justify-content: center;
}
.widget-item-icon {
		width: 45px;
		height: 45px;
		/* 图标和文本的间距 */
		object-fit: contain;
}
.widget-item-text {
		font-size: 14px;
		color: #000;
		text-align: center;
		width: 60%;
}
	
	
	/* 新增的农大新生专栏按钮样式 */
.new-student-container {
			display: flex;
			justify-content: center; /* Center the button */
			align-items: center;
			height: 120px; /* Increased height for the container */
			margin-top: 20px; /* Pull it up to overlap head-image */
			margin-bottom: 0px; /* Spacing below the button */
			padding: 0 10px; /* Padding on sides */
			width: 100%;
			box-sizing: border-box; /* Include padding in width */
}
.new-student-button {
			width: 110%; 
			box-shadow: 0 0 1px;
			height: 105%;
			background: white; /* Gradient background */
			border-radius: 12px; /* More rounded corners */
			overflow: hidden; 
	
			cursor: pointer;
			transition: transform 0.3s ease, box-shadow 0.3s ease;
			display: flex;
			justify-content: center;
			align-items: center;
}
.new-student-button:active {
			transform: scale(0.98); /* Slightly shrink on click */
			box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}
.newStu_image {
			border: #000 1px;
			width: 110%;
			height: 130%;
			object-fit: cover; /* Cover the button area */
			border-radius: 12px; /* Match button border-radius */
}
.new-student-text {
			font-size: 18px; /* Larger font size */
			font-weight: bold;
			color: #fff; /* White text for contrast */
			letter-spacing: 1.5px;
			text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); /* Text shadow for readability */
}
	
