"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Math) {
  CustomModal();
}
const CustomModal = () => "../common/CustomModal.js";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const images = common_vendor.ref([
      {
        src: "https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377c0b52_1754649463.jpg",
        name: "龙子湖校区",
        description: "主校区，位于郑州市龙子湖高校园区"
      },
      {
        src: "https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377cd362_1754649463.png",
        name: "文化路校区",
        description: "百年老校区，省级文物保护单位"
      },
      {
        src: "https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377a3566_1754649463.jpg",
        name: "许昌校区",
        description: "新建校区，现代化教学设施"
      }
    ]);
    const previewImage = (imageUrl) => {
      common_vendor.index.navigateTo({
        url: `/pages/NewStudentKnowledge/detail/detail?imageUrl=${encodeURIComponent(imageUrl)}`
      });
    };
    const showCustomModal = common_vendor.ref(false);
    const closeCustomModal = () => {
      showCustomModal.value = false;
    };
    const copy3DMapLink = () => {
      const mapUrl = "https://www.720yun.com/vr/315z05drknk?s=332068";
      common_vendor.index.setClipboardData({
        data: mapUrl,
        success: function() {
          showCustomModal.value = true;
        },
        fail: function() {
          common_vendor.index.showToast({
            title: "复制失败，请手动复制",
            icon: "none"
          });
        }
      });
    };
    const navigateToLandscape = () => {
      common_vendor.index.navigateTo({
        url: "/pages/NewStudentKnowledge/landscape/landscape"
      });
    };
    const isModalShow = common_vendor.ref(false);
    const modalContent = common_vendor.ref(`河南农业大学IT工作室隶属于学校信息化办公室，主要负责校园信息化建设与维护。

招募方向：
- 前端开发(Web/小程序)
- 后端开发
- UI/UX设计
- 运维技术

有意向的同学请关注"河南农业大学信息化办公室"公众号了解详情！`);
    const openModal = () => {
      isModalShow.value = true;
    };
    const closeModal = () => {
      isModalShow.value = false;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(images.value, (item, index, i0) => {
          return {
            a: item.src,
            b: common_vendor.t(item.name),
            c: common_vendor.t(item.description),
            d: index,
            e: common_vendor.o(($event) => previewImage(item.src), index)
          };
        }),
        b: common_vendor.o(copy3DMapLink),
        c: common_vendor.o(navigateToLandscape),
        d: showCustomModal.value
      }, showCustomModal.value ? {
        e: common_vendor.o(closeCustomModal),
        f: common_vendor.o(() => {
        }),
        g: common_vendor.o(closeCustomModal)
      } : {}, {
        h: common_vendor.o(openModal),
        i: common_vendor.o(closeModal),
        j: common_vendor.p({
          visible: isModalShow.value,
          title: "河南农业大学IT工作室",
          content: modalContent.value,
          confirmText: "好的",
          showCancel: false
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e4150081"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/NewStudentKnowledge/map/index.js.map
