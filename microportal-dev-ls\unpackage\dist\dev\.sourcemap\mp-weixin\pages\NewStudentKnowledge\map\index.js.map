{"version": 3, "file": "index.js", "sources": ["pages/NewStudentKnowledge/map/index.vue", "../../test/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvTmV3U3R1ZGVudEtub3dsZWRnZS9tYXAvaW5kZXgudnVl"], "sourcesContent": ["<template>\n\t<view class=\"page-container\">\n\t\t<view class=\"content-wrapper\">\n\t\t\t<view class=\"header-container\">\n\t\t\t\t<text class=\"header-title\">校区地图导航</text>\n\t\t\t\t<text class=\"header-subtitle\">探索河南农业大学的三大校区</text>\n\t\t\t\t<text class=\"sub-text\">点击图片可查看详细地图</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"images-display-container\">\n\t\t\t\t<view v-for=\"(item, index) in images\" :key=\"index\" class=\"campus-image-item\" @tap=\"previewImage(item.src)\">\n\t\t\t\t\t<view class=\"image-wrapper\">\n\t\t\t\t\t\t<image :src=\"item.src\" mode=\"aspectFill\" class=\"campus-image\" />\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"campus-info\">\n\t\t\t\t\t\t<text class=\"campus-name\">{{ item.name }}</text>\n\t\t\t\t\t\t<text class=\"campus-desc\">{{ item.description }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"buttons-container\">\n\t\t\t\t<button class=\"action-button map-button\" @click=\"copy3DMapLink\">\n\t\t\t\t\t<text class=\"iconfont icon-3d\"></text>\n\t\t\t\t\t<text class=\"button-text\">龙子湖校区3D导览</text>\n\t\t\t\t</button>\n\t\t\t\t<button class=\"action-button photo-button\" @click=\"navigateToLandscape\">\n\t\t\t\t\t<text class=\"iconfont icon-photo\"></text>\n\t\t\t\t\t<text class=\"button-text\">农大风景照片</text>\n\t\t\t\t</button>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 自定义弹窗逻辑保持不变 -->\n\t\t<view class=\"custom-modal-overlay\" v-if=\"showCustomModal\" @tap=\"closeCustomModal\">\n\t\t\t<view class=\"custom-modal-content\" @tap.stop>\n\t\t\t\t<text class=\"modal-title\">温馨提示</text>\n\t\t\t\t<text class=\"modal-message\">3D地图链接已复制到剪贴板。由于微信环境限制，请粘贴到浏览器中查看完整地图。</text>\n\t\t\t\t<button class=\"modal-button\" @tap=\"closeCustomModal\">知道了</button>\n\t\t\t</view>\n\t\t</view>\r\n\t\t<view class=\"bottom\">\r\n\t\t\t<text>注:图源于 共青团河南农业大学委员会微晓工作室</text>\r\n\t\t\t<br />\r\n\t\t\t<text>3D校园源于河南农业大学信管学院</text>\r\n\t\t</view>\r\n\t<text class=\"bottom1\" @click=\"openModal\">河南农业大学IT工作室(欢迎加入)</text>\r\n\t  <!-- 引入自定义模态框组件 -->\r\n\t<CustomModal \r\n\t    :visible=\"isModalShow\" \r\n\t    title=\"河南农业大学IT工作室\" \r\n\t    :content=\"modalContent\" \r\n\t    confirmText=\"好的\" \r\n\t    :showCancel=\"false\" \r\n\t    @confirm=\"closeModal\"\r\n\t  />\t\t\n\t</view>\n</template>\n\n<script setup>\nimport { ref } from 'vue';\n\nconst images = ref([\r\n\n\t{\n\t\tsrc: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377c0b52_1754649463.jpg',\n\t\tname: '龙子湖校区',\n\t\tdescription: '主校区，位于郑州市龙子湖高校园区',\n\t},\n\t{\n\t\tsrc: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377cd362_1754649463.png',\n\t\tname: '文化路校区',\n\t\tdescription: '百年老校区，省级文物保护单位',\n\t},\r\n\t{\r\n\t\tsrc: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377a3566_1754649463.jpg',\r\n\t\tname: '许昌校区',\r\n\t\tdescription: '新建校区，现代化教学设施',\r\n\t}\n]);\n\n// 图片预览逻辑已修改为跳转到详情页\nconst previewImage = (imageUrl) => {\n\tuni.navigateTo({\n\t\turl: `/pages/NewStudentKnowledge/detail/detail?imageUrl=${encodeURIComponent(imageUrl)}`,\n\t});\n};\n\n// 自定义弹窗逻辑\nconst showCustomModal = ref(false);\n\nconst closeCustomModal = () => {\n\tshowCustomModal.value = false;\n};\n\n// 3D地图链接复制逻辑\nconst copy3DMapLink = () => {\n\tconst mapUrl = 'https://www.720yun.com/vr/315z05drknk?s=332068';\n\n\tuni.setClipboardData({\n\t\tdata: mapUrl,\n\t\tsuccess: function () {\n\t\t\tshowCustomModal.value = true;\n\t\t},\n\t\tfail: function () {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '复制失败，请手动复制',\n\t\t\t\ticon: 'none',\n\t\t\t});\n\t\t},\n\t});\n};\n\n// 新增的跳转函数\nconst navigateToLandscape = () => {\n\tuni.navigateTo({\n\t\turl: '/pages/NewStudentKnowledge/landscape/landscape',\n\t});\n};\r\n\r\n\r\n// 引入组件（路径按实际调整，比如 components/CustomModal.vue）\nimport CustomModal from '../common/CustomModal.vue'; \n\nconst isModalShow = ref(false);\n// 内容里直接写 \\n ，配合组件里的 white-space: pre-wrap 即可换行\nconst modalContent = ref(`河南农业大学IT工作室隶属于学校信息化办公室，主要负责校园信息化建设与维护。\n\n招募方向：\n- 前端开发(Web/小程序)\n- 后端开发\n- UI/UX设计\n- 运维技术\n\n有意向的同学请关注\"河南农业大学信息化办公室\"公众号了解详情！`);\n\n// 打开模态框\nconst openModal = () => {\n  isModalShow.value = true;\n};\n\n// 关闭模态框（确认按钮回调）\nconst closeModal = () => {\n  isModalShow.value = false;\n  // 这里可写确认后的逻辑，比如埋点、跳转等\n};\r\n\r\n\n</script>\n\n<style lang=\"scss\" scoped>\n// 基础样式优化\n.page-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 50%, #fff0f5 100%);\n\tpadding: 30rpx;\n\tbox-sizing: border-box;\n}\n\n// 内容区域优化\n.content-wrapper {\n\t// 移除背景色，只保留阴影\n\tbackground: transparent;\n\tborder-radius: 30rpx;\n\tbox-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.08);\n\tpadding: 40rpx;\n\tbackdrop-filter: blur(20rpx);\n}\n\n// 标题样式优化\n.header-container {\n\ttext-align: center;\n\tmargin-bottom: 60rpx;\n\n\t.header-title {\n\t\tfont-size: 48rpx;\n\t\tfont-weight: 800;\n\t\t// 渐变文字效果\n\t\tbackground: linear-gradient(45deg, #2c5364, #203a43);\n\t\t-webkit-background-clip: text;\n\t\tcolor: transparent;\n\t\tmargin-bottom: 20rpx;\n\t\tdisplay: block;\n\t\t// 增加描边，让标题更突出\n\t\t-webkit-text-stroke: 1rpx #fff;\n\t\ttext-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.header-subtitle {\n\t\tfont-size: 32rpx;\n\t\tcolor: #000000; // 调整为黑色\n\t\tmargin-bottom: 10rpx;\n\t\tdisplay: block;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.sub-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #555500; // 调整为浅色\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n\t}\n}\n\n// 图片卡片样式优化\n.images-display-container {\n\tmargin-top: 40rpx;\n}\n\n.campus-image-item {\n\tmargin-bottom: 40rpx;\n\tborder-radius: 20rpx;\n\toverflow: hidden;\n\t// 移除白色背景，使用半透明深色背景\n\tbackground: rgba(0, 0, 0, 0.1);\n\tbox-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);\n\ttransition: all 0.3s ease;\n\tcursor: pointer;\n\n\t&:hover {\n\t\ttransform: translateY(-6rpx);\n\t\tbox-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.image-wrapper {\n\t\tposition: relative;\n\t\toverflow: hidden;\n\n\t\t.campus-image {\n\t\t\twidth: 100%;\n\t\t\theight: 360rpx;\n\t\t\ttransition: transform 0.3s ease;\n\t\t}\n\t}\n\n\t.campus-info {\n\t\t// 使用半透明深色背景\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\tpadding: 30rpx;\n\t\tbackdrop-filter: blur(5rpx); // 增加毛玻璃效果\n\n\t\t.campus-name {\n\t\t\tfont-size: 36rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #fff; // 调整为白色\n\t\t\tmargin-bottom: 10rpx;\n\t\t\tdisplay: block;\n\t\t}\n\n\t\t.campus-desc {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #ddd; // 调整为浅色\n\t\t\tline-height: 1.5;\n\t\t\tdisplay: block;\n\t\t}\n\t}\n}\r\n.bottom1{\r\n\tfont-weight: 1000;\r\n\tmargin-top: 20px;\r\n\tmargin-left: 90px;\r\n}\n\n// 按钮样式优化\n.buttons-container {\n\tmargin-top: 40rpx;\n\tdisplay: flex;\n\tjustify-content: center;\n\tgap: 30rpx; // 按钮之间的间距\n\tflex-wrap: wrap; // 允许按钮换行\n}\n\n.action-button {\n\t// 公共样式\n\tpadding: 25rpx 30rpx;\n\tborder-radius: 50rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 20rpx;\n\tborder: none;\n\t&:after {\n\t\tborder: none;\n\t}\n\t.button-text {\n\t\tfont-size: 22rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #fff;\n\t}\n\t&:active {\n\t\ttransform: scale(0.98);\n\t}\n}\n\n.map-button {\n\tbackground: linear-gradient(45deg, #00b09b, #96c93d);\n}\n\n.photo-button {\n\tbackground: linear-gradient(45deg, #ff6b6b, #f06595);\n}\n\n// 响应式布局优化\n@media screen and (min-width: 768px) {\n\t.images-display-container {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(3, 1fr);\n\t\tgap: 30rpx;\n\t}\n\t.campus-image-item {\n\t\tmargin-bottom: 0;\n\t}\n}\n\n/* 自定义美化弹窗样式 */\n.custom-modal-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground-color: rgba(0, 0, 0, 0.6);\n\tz-index: 1000;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tanimation: fadeIn 0.3s ease-out;\n}\n\n.custom-modal-content {\n\tbackground-color: #ffffff;\n\tborder-radius: 24rpx;\n\tpadding: 50rpx;\n\tmargin: 40rpx;\n\tmax-width: 600rpx;\n\twidth: 90%;\n\tbox-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\ttext-align: center;\n\tanimation: slideIn 0.3s ease-out;\n}\n\n.modal-title {\n\tfont-size: 38rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 25rpx;\n}\n\n.modal-message {\n\tfont-size: 30rpx;\n\tcolor: #555;\n\tline-height: 1.6;\n\tmargin-bottom: 40rpx;\n}\n\n.modal-button {\n\tbackground: linear-gradient(45deg, #4a90ee, #62b0ff);\n\tcolor: #fff;\n\tborder-radius: 40rpx;\n\tfont-size: 22rpx;\n\theight: 70rpx;\n\twidth: 80%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 6rpx 15rpx rgba(74, 144, 238, 0.3);\n\ttransition: all 0.2s ease-in-out;\n\tborder: none;\n\t&:after {\n\t\tborder: none;\n\t}\n\t&:active {\n\t\ttransform: translateY(2rpx);\n\t\tbox-shadow: 0 3rpx 8rpx rgba(74, 144, 238, 0.5);\n\t}\n}\n\n/* 动画效果 */\n@keyframes fadeIn {\n\tfrom {\n\t\topacity: 0;\n\t}\n\tto {\n\t\topacity: 1;\n\t}\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n.bottom{\r\n\tmargin-left: 40px;\r\n\tmargin-top: 10px;\r\n\tfont-size: 10px;\t\r\n\tcolor: #555;\r\n\tfont-weight: 10px;\r\n\ttext-align: center;\r\n\t}\r\n\t\r\n\t\r\n\t\r\n\t\r\n\t\r\n\t\r\n\t\r\n\t\r\n\t\r\n\t\r\n\t\r\n\t\r\n\n\n@keyframes slideIn {\n\tfrom {\n\t\ttransform: translateY(-50px);\n\t\topacity: 0;\n\t}\n\tto {\n\t\ttransform: translateY(0);\n\t\topacity: 1;\n\t}\n}\n</style>\n", "import MiniProgramPage from 'D:/新生小程序/microportal-dev-ls/pages/NewStudentKnowledge/map/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni"], "mappings": ";;;;;AA0HA,MAAM,cAAc,MAAW;;;;AA5D/B,UAAM,SAASA,cAAAA,IAAI;AAAA,MAElB;AAAA,QACC,KAAK;AAAA,QACL,MAAM;AAAA,QACN,aAAa;AAAA,MACb;AAAA,MACD;AAAA,QACC,KAAK;AAAA,QACL,MAAM;AAAA,QACN,aAAa;AAAA,MACb;AAAA,MACD;AAAA,QACC,KAAK;AAAA,QACL,MAAM;AAAA,QACN,aAAa;AAAA,MACb;AAAA,IACF,CAAC;AAGD,UAAM,eAAe,CAAC,aAAa;AAClCC,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,qDAAqD,mBAAmB,QAAQ,CAAC;AAAA,MACxF,CAAE;AAAA,IACF;AAGA,UAAM,kBAAkBD,cAAAA,IAAI,KAAK;AAEjC,UAAM,mBAAmB,MAAM;AAC9B,sBAAgB,QAAQ;AAAA,IACzB;AAGA,UAAM,gBAAgB,MAAM;AAC3B,YAAM,SAAS;AAEfC,oBAAAA,MAAI,iBAAiB;AAAA,QACpB,MAAM;AAAA,QACN,SAAS,WAAY;AACpB,0BAAgB,QAAQ;AAAA,QACxB;AAAA,QACD,MAAM,WAAY;AACjBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;AAGA,UAAM,sBAAsB,MAAM;AACjCA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IACF;AAMA,UAAM,cAAcD,cAAAA,IAAI,KAAK;AAE7B,UAAM,eAAeA,cAAG,IAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCAQO;AAGhC,UAAM,YAAY,MAAM;AACtB,kBAAY,QAAQ;AAAA,IACtB;AAGA,UAAM,aAAa,MAAM;AACvB,kBAAY,QAAQ;AAAA,IAEtB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChJA,GAAG,WAAW,eAAe;"}