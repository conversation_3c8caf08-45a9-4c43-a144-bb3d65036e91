/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-17384c66 {
  display: flex;
  flex-direction: column;
  height: 100vh;
  /* 容器高度占满整个视口 */
  background-color: #f7f7f7;
  /* 页面背景色 */
}
.header-status-bar.data-v-17384c66 {
  height: var(--status-bar-height);
  /* uni-app 提供的状态栏高度变量 */
  width: 100%;
  background-color: #ffffff;
}
.header.data-v-17384c66 {
  padding: 10px;
  background-color: #ffffff;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  /* 底部阴影效果 */
}
.bottom.data-v-17384c66 {
  margin-top: 90px;
  margin-left: 60px;
  font-size: 15px;
  font-weight: 1000;
  text-align: center;
  color: #7a776f;
}
.header-title.data-v-17384c66 {
  font-size: 28px;
  font-weight: bold;
  color: #333333;
}
.header-subtitle.data-v-17384c66 {
  font-size: 14px;
  color: #999999;
}
.card-list.data-v-17384c66 {
  flex: 1;
  /* 占据剩余垂直空间 */
  padding: 10px;
  /* 列表内边距，提供左右留白 */
  overflow-y: auto;
  /* 允许垂直滚动 */
  max-width: 350px;
  margin: 0 auto;
  /* 左右自动外边距，实现居中 */
}
.card.data-v-17384c66 {
  position: relative;
  /* 相对定位，用于子元素的绝对定位 */
  height: 200px;
  margin-bottom: 5px;
  /* 卡片之间的间距 */
  border-radius: 15px;
  overflow: hidden;
  /* 隐藏超出圆角的部分 */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  /* 卡片阴影 */
  display: flex;
  /* 使用 flex 布局 */
  align-items: flex-end;
  /* 内容垂直对齐到底部 */
  transition: transform 0.2s ease-in-out;
  /* 点击时的动画效果 */
}
.card.data-v-17384c66:active {
  transform: scale(0.98);
  /* 点击时轻微缩小 */
}
.card-image.data-v-17384c66 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  /* 位于最底层 */
  object-fit: cover;
  /* 保持图片比例并填充容器 */
}
.card-overlay.data-v-17384c66 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);
  /* 从下到上的黑色渐变 */
  z-index: 2;
  /* 位于图片之上，内容之下 */
}
.color-card-1.data-v-17384c66 {
  margin-top: 5px;
  background-color: #8c738e;
  /* 紫色系 */
}
.color-card-2.data-v-17384c66 {
  background-color: #4a90e2;
  /* 蓝色系 */
}
.grey-card.data-v-17384c66 {
  background-color: #e0e0e0;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  /* 较浅的阴影 */
}
.card-content.data-v-17384c66 {
  width: 100%;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 3;
  /* 确保内容在最上层 */
  color: #ffffff;
  /* 默认文字颜色为白色 */
}
.grey-card .card-content.data-v-17384c66 {
  color: #333333;
}
.card-text-container.data-v-17384c66 {
  display: flex;
  flex-direction: column;
}
.card-title.data-v-17384c66 {
  color: white;
  font-size: 20px;
  font-weight: bold;
}
.card-subtitle.data-v-17384c66 {
  color: white;
  font-size: 14px;
  margin-top: 5px;
}
.card-arrow-text.data-v-17384c66 {
  font-size: 24px;
  font-weight: bold;
  opacity: 0.6;
  /* 增加透明度，看起来更柔和 */
}
.grey-card .card-arrow-text.data-v-17384c66 {
  color: #666666;
}