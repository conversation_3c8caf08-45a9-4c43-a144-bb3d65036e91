<template>
	<view class="container">
		<view class="head-image">
			<image src="https://itstudio.henau.edu.cn/image_hosting/uploads/6895f7d4c5a85_1754658772.jpg" class="head-bg-img" />
		</view>
		
		
		<!-- 新增的农大新生专栏按钮 -->
				<view class="grid-container new-student-container">
					<view class="new-student-button" @click="goToNewStudentCorner()">
						<image class="newStu_image" src="https://itstudio.henau.edu.cn/image_hosting/uploads/6896230fb9805_1754669839.png"></image>
					</view>
				</view>
		
		
		
		<view class="content">
			<view class="grid-container">
				<view class="widget-container">
					<view class="widget-list">
						<!-- 服务列表 -->
						<view class="widget-item" v-for="(item, index) in widgetData" :key="index"
							@click="goToService(item)">
							<!-- 服务图标 -->
							<view class="icon-container">
								<image :src="
										item.service_icon || '/static/logo.png'
									" class="widget-item-icon" />
							</view>
							<!-- 服务名称 -->
							<text class="widget-item-text">{{
								item.service_name
							}}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="grid-container">
				<view class="service-container">
					<view class="service-title">校园服务</view>
					<view class="service-list">
						<!-- 服务列表 -->
						<view class="grid-item" v-for="(item, index) in servicesData" :key="index"
							@click="goToService(item)">
							<!-- 服务图标 -->
							<image :src="item.service_icon || '/static/logo.png'" class="grid-item-icon" />
							<!-- 服务名称 -->
							<text class="grid-item-text">{{
								item.service_name
							}}</text>
						</view>
						<!-- 更多服务 -->
						<view class="grid-item" @click="moreService()">
							<!-- 服务图标 -->
							<image :src="'/static/icon/more-app.png'" class="grid-item-icon" />
							<!-- 服务名称 -->
							<text class="grid-item-text">更多服务</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="copyright">
			<view class="version-number">MicroService v2.4.2</view>
			<view class="technical-support">技术支持：河南农业大学IT工作室</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref
	} from "vue";

	import {
		onLoad,
		onShareAppMessage,
		onShareTimeline
	} from "@dcloudio/uni-app";
	import {
		useBaseStore
	} from "@/stores/base";
	const baseStore = useBaseStore();
	// import {
	// 	urlToObj
	// } from '../../common/utils';
	// const title = 'Hello';
	// const quer = ref("");
	// 应用服务数据
	const servicesData = ref([{
			service_module_id: 1,
			service_module_name: "综合服务",
			service_id: 2,
			service_name: "本科生请假",
			service_type: "H5APP",
			service_icon: "/static/icon/GeneralService/UndergraduateLeave.png",
			service_url: "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxf40cdbdcc58c583e&redirect_uri=https%3a%2f%2fstudqj.henau.edu.cn&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect",
			service_order: 100,
			service_module_order: 1,
		},
		{
			service_module_id: 1,
			service_module_name: "综合服务",
			service_id: 4,
			service_name: "实验室管理",
			service_type: "H5APP",
			service_icon: "/static/icon/GeneralService/Laboratory.png",
			service_url: "https://oauth.henau.edu.cn/oauth2_qr_connect/login?appid=nd35b284cce2d94f0d&redirect_uri=https%3a%2f%2fsysxxh.henau.edu.cn%2foauthlogin.aspx&response_type=code&scope=henauapi_login&state=app",
			service_order: 100,
			service_module_order: 1,
		},
		{
			service_module_id: 1,
			service_module_name: "综合服务",
			service_id: 37,
			service_name: "校园服务电话",
			service_type: "H5APP",
			service_icon: "/static/icon/GeneralService/Telephone.png",
			service_url: "https://microservices.henau.edu.cn/henauwfw/#/Telephone",
			service_order: 100,
			service_module_order: 1,
		},
		{
			service_module_id: 1,
			service_module_name: "综合服务",
			service_id: 43,
			service_name: "校内业务用表",
			service_type: "H5APP",
			service_icon: "/static/icon/GeneralService/BusinessForm.png",
			service_url: "https://microservices.henau.edu.cn/henauwfw/#/BusinessForm",
			service_order: 100,
			service_module_order: 1,
		},
		{
			service_module_id: 5,
			service_module_name: "建议反馈",
			service_id: 34,
			service_name: "灵感小站",
			service_type: "H5APP",
			service_icon: "/static/icon/SuggestionFeedback/InspirationStation.png",
			service_url: "https://microservices.henau.edu.cn/henauwfw/#/SubmitFeedback",
			service_order: 100,
			service_module_order: 5,
		},

		{
			service_module_id: 1,
			service_module_name: "综合服务",
			service_id: 57,
			service_name: "VPN登录",
			service_type: "H5APP",
			service_icon: "/static/icon/GeneralService/VPNLogin.png",
			service_url: "https://vpn2.henau.edu.cn/portal/",
			service_order: 100,
			service_module_order: 1,
		},
		{
			service_module_id: 4,
			service_module_name: "我的信息",
			service_id: 38,
			service_name: "我的消息",
			service_type: "H5APP",
			service_icon: "/static/icon/GeneralService/MyMessage.png",
			service_url: "https://microservices.henau.edu.cn/henauwfw/#/MyMessage",
			service_order: 100,
			service_module_order: 4,
		},
		// {
		// 	service_module_id: 3,
		// 	service_module_name: "学习服务",
		// 	service_id: 25,
		// 	service_name: "课表查询",
		// 	service_type: "WXAPP",
		// 	service_icon: "/static/icon/LearningService/ScheduleInquiry.png",
		// 	service_url: "",
		// 	wxapp_id: "wx5da532e7f5b2afaf",
		// 	service_order: 100,
		// 	service_module_order: 3,
		// },
		{
			service_module_id: 3,
			service_module_name: "综合服务",
			service_id: 18,
			service_name: "校园网注册",
			service_type: "H5APP",
			service_icon: "/static/icon/ResourceApply/CampusNetwork.png",
			service_url: "https://oauth.henau.edu.cn/app/CNPRS",
			service_order: 100,
			service_module_order: 3,
		},
		{
			service_module_id: 3,
			service_module_name: "学习服务",
			service_id: 30,
			service_name: "电子成绩单",
			service_type: "WXAPP",
			service_icon: "/static/icon/LearningService/Transcript.png",
			service_url: "",
			wxapp_id: "wx5da532e7f5b2afaf",
			service_order: 100,
			service_module_order: 3,
		},
		{
			service_module_id: 1,
			service_module_name: "综合服务",
			service_id: 45,
			service_name: "校园地图",
			service_type: "H5APP",
			service_icon: "/static/icon/GeneralService/henaumap.svg",
			service_url: "https://henaumap.henau.edu.cn/",
			service_order: 100,
			service_module_order: 1,
		},
		{
			service_module_id: 1,
			service_module_name: "综合服务",
			service_id: 58,
			service_name: "OA办公系统",
			service_type: "H5APP",
			service_icon: "/static/icon/GeneralService/OA.png",
			service_url: "https://oauth.henau.edu.cn/oauth2_qr_connect/login?appid=ndfe16be378bbef3a1&redirect_uri=https%3A%2F%2Foa.henau.edu.cn/sso.php&response_type=code&scope=henauapi_login&state=1_",
			service_order: 100,
			service_module_order: 1,
		},
	]);

	// 组件服务数据
	const widgetData = ref([{
			service_id: 1,
			service_name: "校园卡付款码",
			service_type: "H5APP",
			service_icon: "/static/icon/GeneralService/CampusCard.png",
			service_url: "https://yktwx.henau.edu.cn/berserker-auth/wechat/token/mp?resultUrl=https%3A%2F%2Fyktwx.henau.edu.cn%2Fplat%2Fpay%3FappId%3D12%26loginFrom%3Dwechat-mp%26synAccessSource%3Dwechat-mp%26nodeId%3D-12",
		},
		// {
		// 	service_id: 2,
		// 	service_name: "校园网注册",
		// 	service_type: "H5APP",
		// 	service_icon: "/static/icon/ResourceApply/CampusNetwork.png",
		// 	service_url: "https://oauth.henau.edu.cn/app/CNPRS",
		// },
		{
			service_id: 2,
			service_name: "访客预约",
			service_type: "H5APP",
			service_icon: "/static/icon/GeneralService/Visitor.png",
			service_url: "https://bwcfr.henau.edu.cn/visitor/#/pages/index/index",
		},
		{
			service_id: 3,
			service_name: "失物招领",
			service_type: "H5APP",
			service_icon: "/static/icon/GeneralService/LosingStuff.svg",
			service_url: "https://swzl.henau.edu.cn/swzl/feed/index",
		},
		{
			service_id: 4,
			service_name: "农宝圈",
			service_type: "H5APP",
			service_icon: "/static/icon/GeneralService/HenauMoments.png",
			service_url: "https://moments.henau.edu.cn/#/Index",
		},
	]);
	
	
	
	
	// 新增的农大新生专栏点击事件
		const goToNewStudentCorner = () => {
			uni.switchTab({
				url: '/pages/NewStudentKnowledge/index/index',
			});
		};

	// 服务点击事件
	const goToService = (item) => {
		if (item.service_type === "H5APP") {
			// 如果是 H5 页面，跳转到 Webview
			uni.navigateTo({
				url: `/pages/serviceWebView/serviceWebView?webviewUrl=${encodeURIComponent(
				item.service_url
			)}`,
			});
		} else if (item.service_type === "WXAPP") {
			// 如果是小程序，跳转到指定的小程序
			uni.navigateToMiniProgram({
				appId: item.wxapp_id,
				path: item.service_url,
				success(res) {
					console.log("小程序跳转成功", res);
				},
				fail(err) {
					console.log("小程序跳转失败", err);
				},
			});
		} else {
			console.log("无法识别的服务类型");
		}
	};

	// 更多服务
	const moreService = () => {
		uni.navigateTo({
			url: "/pages/microservices/microservices",
		});
	};
	// 分享给朋友和朋友圈
	onShareAppMessage(() => {});
	onShareTimeline(() => {});

	onLoad((query) => {
		// 获取由webview分享页面携带的参数
		const webViewUrl = decodeURIComponent(query.webViewUrl);
		// 获取扫码参数，对于扫描电子通行证进入的场景，使用单独的webview页面处理
		const q = decodeURIComponent(query.q); // 获取到二维码原始链接内容
		const scancode_time = parseInt(query.scancode_time); // 获取用户扫码时间 UNIX 时间戳
		// 微信webview在点击右下角图标返回小程序首页时q会是字符串'undefined'，需要进行过滤
		if (q && q != undefined && q != "undefined") {
			// 指定的电子通行证域名列表
			const acDomains = ["ac.henau.edu.cn"];
			const containsAllowedDomain = (url) => {
				return acDomains.some((domain) => url.includes(domain));
			};
			// 对于电子通行证进行单独处理
			if (containsAllowedDomain(q)) {
				// uni.showModal({
				// 	title: "提示",
				// 	content: q,
				// 	showCancel: true,
				// 	success: ({ confirm, cancel }) => {},
				// });
				uni.navigateTo({
					url: `/pages/ac/ac?webviewUrl=${encodeURIComponent(q)}`,
				});
			} else {
				uni.navigateTo({
					url: `/pages/serviceWebView/serviceWebView?webviewUrl=${encodeURIComponent(
					q
				)}`,
				});
				// uni.showModal({
				// 	title: "提示",
				// 	content: "none",
				// 	showCancel: true,
				// 	success: ({ confirm, cancel }) => {},
				// });
			}
		}
		// 处理分享过来的 webViewUrl
		if (webViewUrl && webViewUrl !== "undefined") {
			uni.navigateTo({
				url: `/pages/serviceWebView/serviceWebView?webviewUrl=${encodeURIComponent(
				webViewUrl
			)}`,
			});
		}
	});
</script>

<style>
	.container {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		/* 确保容器至少占满视口高度 */
	}

	.content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 15px;
		margin-top: 5px;
		background-color: #f8f8f8;
	}

	.grid-container {
		width: 100%;
	}

	.grid-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 100%;
	}

	.grid-item-icon {
		width: 40px;
		height: 40px;
		margin-bottom: 8px;
		/* 图标和文本的间距 */
		object-fit: contain;
	}

	.grid-item-text {
		font-size: 12px;
		/* 字体大小统一设置为12px */
		color: #000;
		text-align: center;
	}

	/* 最后一行不足四个时，自动向左对齐 */
	.grid-container>.grid-item:nth-child(4n + 1) {
		/* 这里是为了避免最后一行空隙，保持左对齐 */
		margin-left: 0;
	}

	.service-container {
		/* 列与列、行与行之间的间距 */
		width: 100%;
		max-width: 1200px;
		/* 控制容器最大宽度 */
		padding: 15px 0;
		background-color: #fff;
		border-radius: 12px;
	}

	.service-list {
		/* width: 100%; */
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		/* 每行四列 */
		grid-gap: 10px;

		/* box-shadow: rgba(50, 50, 105, 0.15) 0px 2px 5px 0px, rgba(0, 0, 0, 0.05) 0px 1px 1px 0px; */
	}

	.service-title {
		color: #999999;
		font-size: 12px;
		margin: 0 0 15px 10px;
	}

	.copyright {
		text-align: center;
		margin-top: auto;
		margin-bottom: 30px;
		color: #999999;
	}

	.version-number {
		cursor: pointer;
		user-select: none;
		font-size: 12px;
		/* 禁止选中文本，有助于减少默认交互效果 */
		-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
		/* 将点击高亮颜色设置为透明，去除点击时的背景色显示 */
	}

	.technical-support {
		cursor: pointer;
		user-select: none;
		font-size: 12px;
		/* 禁止选中文本，有助于减少默认交互效果 */
		-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
		/* 将点击高亮颜色设置为透明，去除点击时的背景色显示 */
		margin-top: 5px;
	}

	.head-image {
		width: 100%;
	}

	.head-bg-img {
		width: 100%;
		height: 150px;
	}

	.widget-container {
		/* 列与列、行与行之间的间距 */
		width: 100%;
		max-width: 1200px;
		/* 控制容器最大宽度 */
		padding: 10px 0;
	}

	.widget-list {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-gap: 15px;
	}

	.widget-item {
		display: flex;
		align-items: center;
		text-align: justify;
		padding: 15px 10px;
		background-color: #fff;
		border-radius: 12px;
	}

	.icon-container {
		width: 40%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.widget-item-icon {
		width: 45px;
		height: 45px;
		/* 图标和文本的间距 */
		object-fit: contain;
	}

	.widget-item-text {
		font-size: 14px;
		color: #000;
		text-align: center;
		width: 60%;
	}
	
	
	/* 新增的农大新生专栏按钮样式 */
		.new-student-container {
			display: flex;
			justify-content: center; /* Center the button */
			align-items: center;
			height: 120px; /* Increased height for the container */
			margin-top: 20px; /* Pull it up to overlap head-image */
			margin-bottom: 0px; /* Spacing below the button */
			padding: 0 10px; /* Padding on sides */
			width: 100%;
			box-sizing: border-box; /* Include padding in width */
		}
	
		.new-student-button {
			width: 110%; 
			box-shadow: 0 0 1px;
			height: 105%;
			background: white; /* Gradient background */
			border-radius: 12px; /* More rounded corners */
			overflow: hidden; 
	
			cursor: pointer;
			transition: transform 0.3s ease, box-shadow 0.3s ease;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	
		.new-student-button:active {
			transform: scale(0.98); /* Slightly shrink on click */
			box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
		}
	
		.newStu_image {
			border: #000 1px;
			width: 110%;
			height: 130%;
			object-fit: cover; /* Cover the button area */
			border-radius: 12px; /* Match button border-radius */
		}
	
		.new-student-text {
			font-size: 18px; /* Larger font size */
			font-weight: bold;
			color: #fff; /* White text for contrast */
			letter-spacing: 1.5px;
			text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); /* Text shadow for readability */
		}
	
</style>