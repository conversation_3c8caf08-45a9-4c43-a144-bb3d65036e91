"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/microservices/microservices.js";
  "./pages/serviceWebView/serviceWebView.js";
  "./pages/telephone/telephone.js";
  "./pages/businessForm/businessForm.js";
  "./pages/henaumap/henaumap.js";
  "./pages/studqj/studqj.js";
  "./pages/sysxxh/sysxxh.js";
  "./pages/oauthAppCNPRS/oauthAppCNPRS.js";
  "./pages/myMessage/myMessage.js";
  "./pages/yktwx/yktwx.js";
  "./pages/ac/ac.js";
  "./pages/visitorReservation/visitorReservation.js";
  "./pages/NewStudentKnowledge/index/index.js";
  "./pages/NewStudentKnowledge/navigation/index.js";
  "./pages/NewStudentKnowledge/map/index.js";
  "./pages/NewStudentKnowledge/comment/index.js";
  "./pages/NewStudentKnowledge/landscape/landscape.js";
  "./pages/NewStudentKnowledge/detail/detail.js";
  "./pages/NewStudentKnowledge/xuchang/xuchang.js";
  "./pages/NewStudentKnowledge/longzihu/longzihu.js";
  "./pages/NewStudentKnowledge/wenhua/wenhua.js";
  "./pages/NewStudentKnowledge/changyong/changyong.js";
  "./pages/NewStudentKnowledge/web-view/web-view.js";
  "./pages/NewStudentKnowledge/test/test.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.index.__f__("log", "at App.vue:4", "App Launch");
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:7", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:10", "App Hide");
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  app.use(common_vendor.createPinia());
  return {
    app,
    Pinia: common_vendor.Pinia
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
