/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 返回顶部按钮和阅读进度样式 */
.scroll-controls.data-v-036d9ace {
  position: fixed;
  bottom: 130rpx;
  /* 距离底部 */
  right: 20rpx;
  /* 距离右侧 */
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 99;
  /* 确保在内容之上，但在弹窗之下 */
}
.reading-progress.data-v-036d9ace {
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 24rpx;
  padding: 10rpx 20rpx;
  border-radius: 30rpx;
  margin-bottom: 10rpx;
  white-space: nowrap;
  /* 防止百分比换行 */
}
.back-to-top-button.data-v-036d9ace {
  background-color: #0f4c81;
  /* 匹配主题色 */
  color: #fff;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  /* 圆形按钮 */
  display: flex;
  /* 使用flexbox来居中图片 */
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
  border: none;
  padding: 0;
  /* 移除默认padding */
}
.top-arrow-icon.data-v-036d9ace {
  width: 80rpx;
  /* 图片大小 */
  height: 80rpx;
  /* 可以添加滤镜来改变颜色，如果图片是黑色的 */
  /* filter: invert(100%); */
}
.container.data-v-036d9ace {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f0f2f5;
  /* 更柔和的页面背景色 */
}

/* 顶部状态栏占位，用于适配刘海屏等设备 */
.status-bar-placeholder.data-v-036d9ace {
  height: var(--status-bar-height);
  /* uni-app 提供的状态栏高度变量 */
  width: 100%;
  background-color: #ffffff;
  /* 状态栏背景色 */
}
.main-scroll-view.data-v-036d9ace {
  flex: 1;
  /* 占据剩余空间，允许内容滚动 */
  box-sizing: border-box;
  /* 边框盒模型 */
}
.content-wrapper.data-v-036d9ace {
  width: 100%;
  max-width: 750px;
  /* 限制内容最大宽度，模拟原 HTML 的 750px */
  margin: 0 auto;
  /* 居中显示 */
  background-color: #ffffff;
  /* 内容区域背景色 */
  padding: 20px;
  /* 增加内容区域内边距 */
  box-sizing: border-box;
  /* 边框盒模型 */
  font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Helvetica Neue", "Microsoft YaHei UI", "Microsoft YaHei", Arial, sans-serif;
  font-size: 16px;
  line-height: 1.8;
  /* 增加行高，提升可读性 */
  color: #333333;
  /* 默认文字颜色 */
  border-radius: 12px;
  /* 添加圆角 */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  /* 柔和的阴影 */
}

/* 欢迎标题样式 */
.welcome-title-wrapper.data-v-036d9ace {
  text-align: center;
  margin: 1.5em auto 1.5em;
  /* 调整上下间距 */
  padding: 0 1em 10px;
  /* 增加底部内边距 */
  border-bottom: 3px solid #0F4C81;
  /* 稍微加粗底部边框 */
  display: flex;
  justify-content: center;
  align-items: center;
  width: -webkit-fit-content;
  width: fit-content;
  margin-top: 0;
}
.welcome-title.data-v-036d9ace {
  font-size: 22px;
  /* 稍微增大字体 */
  font-weight: bold;
  line-height: 1.5;
  /* 调整行高 */
  color: #2c3e50;
  /* 更深沉的颜色 */
  white-space: nowrap;
}

/* 新同学问候样式 */
.greeting-section.data-v-036d9ace {
  padding-left: 12px;
  /* 增加左侧内边距 */
  border-left: 4px solid #0F4C81;
  /* 稍微加粗左侧边框 */
  margin: 2.5em 0 1.2em 0;
  /* 增加上下间距 */
}
.greeting-text.data-v-036d9ace {
  font-size: 18px;
  /* 稍微增大字体 */
  line-height: 1.3;
  font-weight: bold;
  color: #34495e;
  /* 更深沉的颜色 */
}

/* 各章节标题样式 */
.section-title-wrapper.data-v-036d9ace {
  text-align: center;
  margin: 4em auto 2.5em;
  /* 增加上下间距 */
  padding: 8px 15px;
  /* 增加内边距，形成“药丸”形状 */
  background: linear-gradient(to right, #0F4C81, #2c7bb6);
  /* 渐变背景 */
  border-radius: 25px;
  /* 圆角，形成“药丸”形状 */
  display: flex;
  justify-content: center;
  align-items: center;
  width: -webkit-fit-content;
  width: fit-content;
  box-shadow: 0 2px 8px rgba(15, 76, 129, 0.3);
  /* 添加阴影 */
}
.section-title.data-v-036d9ace {
  font-size: 20px;
  /* 稍微增大字体 */
  font-weight: bold;
  line-height: 1.5;
  color: #fff;
  white-space: nowrap;
}

/* 普通信息段落样式 */
.info-paragraph.data-v-036d9ace {
  margin: 1.8em 0;
  /* 调整上下间距，左右由 content-wrapper 决定 */
  letter-spacing: 0.05em;
  /* 稍微调整字间距 */
  line-height: 1.8;
  /* 统一行高 */
}

/* 列表信息样式 */
.info-list.data-v-036d9ace {
  padding-left: 0;
  /* 移除 ul/ol 默认的左边距 */
  margin-left: 0;
  line-height: 1.8;
  /* 统一行高 */
}
.list-item.data-v-036d9ace {
  display: flex;
  margin-bottom: 0.8em;
  /* 增加列表项之间的间距 */
  align-items: flex-start;
}
.list-item-number.data-v-036d9ace {
  font-size: 16px;
  line-height: 1.8;
  margin-right: 0.6em;
  /* 调整数字和内容之间的间距 */
  flex-shrink: 0;
  font-weight: bold;
  /* 数字加粗 */
  color: #0F4C81;
  /* 数字颜色 */
}
.list-item-content.data-v-036d9ace {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 粗体文本样式 */
.info-strong.data-v-036d9ace {
  font-weight: bold;
  color: #0F4C81;
  /* 保持原蓝色 */
}

/* 普通文本样式 */
.info-text.data-v-036d9ace {
  font-size: 16px;
  color: #333333;
  line-height: 1.8;
  letter-spacing: 0.05em;
  display: block;
}

/* 蓝色粗体文本样式 */
.info-strong-blue.data-v-036d9ace {
  font-weight: bold;
  color: #0F4C81;
}

/* 自定义换行符样式，用于模拟 <br/> */
.newline.data-v-036d9ace {
  display: block;
  height: 0.5em;
  /* 控制换行间距 */
  content: "";
}

/* 底部小字提示 */
.small-text.data-v-036d9ace {
  font-size: 13px;
  /* 适当缩小字体 */
  color: #7f8c8d;
  /* 更柔和的灰色 */
  margin-top: 2em;
  /* 增加与上方内容的间距 */
  line-height: 1.6;
  display: block;
  text-align: justify;
  /* 两端对齐 */
}