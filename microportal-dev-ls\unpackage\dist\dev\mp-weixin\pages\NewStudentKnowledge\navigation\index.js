"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Math) {
  CustomModal();
}
const CustomModal = () => "../common/CustomModal.js";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const navigateTo = (campus) => {
      common_vendor.index.__f__("log", "at pages/NewStudentKnowledge/navigation/index.vue:103", `导航到 ${campus} 校区`);
      common_vendor.index.navigateTo({
        url: `/pages/NewStudentKnowledge/${campus}/${campus}`
      });
    };
    const isModalShow = common_vendor.ref(false);
    const modalContent = common_vendor.ref(`河南农业大学IT工作室隶属于学校信息化办公室，主要负责校园信息化建设与维护。

招募方向：
- 前端开发(Web/小程序)
- 后端开发
- UI/UX设计
- 运维技术

有意向的同学请关注"河南农业大学信息化办公室"公众号了解详情！`);
    const openModal = () => {
      isModalShow.value = true;
    };
    const closeModal = () => {
      isModalShow.value = false;
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => navigateTo("wenhua")),
        b: common_vendor.o(($event) => navigateTo("longzihu")),
        c: common_vendor.o(($event) => _ctx.jumpxuchang()),
        d: common_vendor.o(($event) => navigateTo("xuchang")),
        e: common_vendor.o(($event) => navigateTo("changyong")),
        f: common_vendor.o(openModal),
        g: common_vendor.o(closeModal),
        h: common_vendor.p({
          visible: isModalShow.value,
          title: "河南农业大学IT工作室",
          content: modalContent.value,
          confirmText: "好的",
          showCancel: false
        })
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-17384c66"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/NewStudentKnowledge/navigation/index.js.map
