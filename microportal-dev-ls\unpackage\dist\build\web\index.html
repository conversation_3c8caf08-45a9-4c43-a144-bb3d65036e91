<!DOCTYPE html>
<html lang="en">
  <head>
    <link rel="stylesheet" href="/assets/uni.de19c6ed.css">

    <meta charset="UTF-8" />
    <script>
      var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
        CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
        (coverSupport ? ', viewport-fit=cover' : '') + '" />')
    </script>
    <title>microportal</title>
    <!--preload-links-->
    <!--app-context-->
    <script type="module" crossorigin src="/assets/index-DeHPfleJ.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-Z1BkRUu_.css">
  </head>
  <body>
    <div id="app"><!--app-html--></div>
  </body>
</html>
