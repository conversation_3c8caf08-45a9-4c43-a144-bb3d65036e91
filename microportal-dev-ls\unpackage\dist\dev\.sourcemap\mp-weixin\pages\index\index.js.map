{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "../../test/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<view class=\"head-image\">\n\t\t\t<image src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6895f7d4c5a85_1754658772.jpg\" class=\"head-bg-img\" />\n\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t<!-- 新增的农大新生专栏按钮 -->\r\n\t\t\t\t<view class=\"grid-container new-student-container\">\r\n\t\t\t\t\t<view class=\"new-student-button\" @click=\"goToNewStudentCorner()\">\r\n\t\t\t\t\t\t<image class=\"newStu_image\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6896230fb9805_1754669839.png\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\r\n\t\t\r\n\t\t\n\t\t<view class=\"content\">\n\t\t\t<view class=\"grid-container\">\n\t\t\t\t<view class=\"widget-container\">\n\t\t\t\t\t<view class=\"widget-list\">\n\t\t\t\t\t\t<!-- 服务列表 -->\n\t\t\t\t\t\t<view class=\"widget-item\" v-for=\"(item, index) in widgetData\" :key=\"index\"\n\t\t\t\t\t\t\t@click=\"goToService(item)\">\n\t\t\t\t\t\t\t<!-- 服务图标 -->\n\t\t\t\t\t\t\t<view class=\"icon-container\">\n\t\t\t\t\t\t\t\t<image :src=\"\n\t\t\t\t\t\t\t\t\t\titem.service_icon || '/static/logo.png'\n\t\t\t\t\t\t\t\t\t\" class=\"widget-item-icon\" />\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<!-- 服务名称 -->\n\t\t\t\t\t\t\t<text class=\"widget-item-text\">{{\n\t\t\t\t\t\t\t\titem.service_name\n\t\t\t\t\t\t\t}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"grid-container\">\n\t\t\t\t<view class=\"service-container\">\n\t\t\t\t\t<view class=\"service-title\">校园服务</view>\n\t\t\t\t\t<view class=\"service-list\">\n\t\t\t\t\t\t<!-- 服务列表 -->\n\t\t\t\t\t\t<view class=\"grid-item\" v-for=\"(item, index) in servicesData\" :key=\"index\"\n\t\t\t\t\t\t\t@click=\"goToService(item)\">\n\t\t\t\t\t\t\t<!-- 服务图标 -->\n\t\t\t\t\t\t\t<image :src=\"item.service_icon || '/static/logo.png'\" class=\"grid-item-icon\" />\n\t\t\t\t\t\t\t<!-- 服务名称 -->\n\t\t\t\t\t\t\t<text class=\"grid-item-text\">{{\n\t\t\t\t\t\t\t\titem.service_name\n\t\t\t\t\t\t\t}}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 更多服务 -->\n\t\t\t\t\t\t<view class=\"grid-item\" @click=\"moreService()\">\n\t\t\t\t\t\t\t<!-- 服务图标 -->\n\t\t\t\t\t\t\t<image :src=\"'/static/icon/more-app.png'\" class=\"grid-item-icon\" />\n\t\t\t\t\t\t\t<!-- 服务名称 -->\n\t\t\t\t\t\t\t<text class=\"grid-item-text\">更多服务</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"copyright\">\n\t\t\t<view class=\"version-number\">MicroService v2.4.2</view>\n\t\t\t<view class=\"technical-support\">技术支持：河南农业大学IT工作室</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup>\n\timport {\n\t\tref\n\t} from \"vue\";\n\n\timport {\n\t\tonLoad,\n\t\tonShareAppMessage,\n\t\tonShareTimeline\n\t} from \"@dcloudio/uni-app\";\n\timport {\n\t\tuseBaseStore\n\t} from \"@/stores/base\";\n\tconst baseStore = useBaseStore();\n\t// import {\n\t// \turlToObj\n\t// } from '../../common/utils';\n\t// const title = 'Hello';\n\t// const quer = ref(\"\");\n\t// 应用服务数据\n\tconst servicesData = ref([{\n\t\t\tservice_module_id: 1,\n\t\t\tservice_module_name: \"综合服务\",\n\t\t\tservice_id: 2,\n\t\t\tservice_name: \"本科生请假\",\n\t\t\tservice_type: \"H5APP\",\n\t\t\tservice_icon: \"/static/icon/GeneralService/UndergraduateLeave.png\",\n\t\t\tservice_url: \"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxf40cdbdcc58c583e&redirect_uri=https%3a%2f%2fstudqj.henau.edu.cn&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect\",\n\t\t\tservice_order: 100,\n\t\t\tservice_module_order: 1,\n\t\t},\n\t\t{\n\t\t\tservice_module_id: 1,\n\t\t\tservice_module_name: \"综合服务\",\n\t\t\tservice_id: 4,\n\t\t\tservice_name: \"实验室管理\",\n\t\t\tservice_type: \"H5APP\",\n\t\t\tservice_icon: \"/static/icon/GeneralService/Laboratory.png\",\n\t\t\tservice_url: \"https://oauth.henau.edu.cn/oauth2_qr_connect/login?appid=nd35b284cce2d94f0d&redirect_uri=https%3a%2f%2fsysxxh.henau.edu.cn%2foauthlogin.aspx&response_type=code&scope=henauapi_login&state=app\",\n\t\t\tservice_order: 100,\n\t\t\tservice_module_order: 1,\n\t\t},\n\t\t{\n\t\t\tservice_module_id: 1,\n\t\t\tservice_module_name: \"综合服务\",\n\t\t\tservice_id: 37,\n\t\t\tservice_name: \"校园服务电话\",\n\t\t\tservice_type: \"H5APP\",\n\t\t\tservice_icon: \"/static/icon/GeneralService/Telephone.png\",\n\t\t\tservice_url: \"https://microservices.henau.edu.cn/henauwfw/#/Telephone\",\n\t\t\tservice_order: 100,\n\t\t\tservice_module_order: 1,\n\t\t},\n\t\t{\n\t\t\tservice_module_id: 1,\n\t\t\tservice_module_name: \"综合服务\",\n\t\t\tservice_id: 43,\n\t\t\tservice_name: \"校内业务用表\",\n\t\t\tservice_type: \"H5APP\",\n\t\t\tservice_icon: \"/static/icon/GeneralService/BusinessForm.png\",\n\t\t\tservice_url: \"https://microservices.henau.edu.cn/henauwfw/#/BusinessForm\",\n\t\t\tservice_order: 100,\n\t\t\tservice_module_order: 1,\n\t\t},\n\t\t{\n\t\t\tservice_module_id: 5,\n\t\t\tservice_module_name: \"建议反馈\",\n\t\t\tservice_id: 34,\n\t\t\tservice_name: \"灵感小站\",\n\t\t\tservice_type: \"H5APP\",\n\t\t\tservice_icon: \"/static/icon/SuggestionFeedback/InspirationStation.png\",\n\t\t\tservice_url: \"https://microservices.henau.edu.cn/henauwfw/#/SubmitFeedback\",\n\t\t\tservice_order: 100,\n\t\t\tservice_module_order: 5,\n\t\t},\n\n\t\t{\n\t\t\tservice_module_id: 1,\n\t\t\tservice_module_name: \"综合服务\",\n\t\t\tservice_id: 57,\n\t\t\tservice_name: \"VPN登录\",\n\t\t\tservice_type: \"H5APP\",\n\t\t\tservice_icon: \"/static/icon/GeneralService/VPNLogin.png\",\n\t\t\tservice_url: \"https://vpn2.henau.edu.cn/portal/\",\n\t\t\tservice_order: 100,\n\t\t\tservice_module_order: 1,\n\t\t},\n\t\t{\n\t\t\tservice_module_id: 4,\n\t\t\tservice_module_name: \"我的信息\",\n\t\t\tservice_id: 38,\n\t\t\tservice_name: \"我的消息\",\n\t\t\tservice_type: \"H5APP\",\n\t\t\tservice_icon: \"/static/icon/GeneralService/MyMessage.png\",\n\t\t\tservice_url: \"https://microservices.henau.edu.cn/henauwfw/#/MyMessage\",\n\t\t\tservice_order: 100,\n\t\t\tservice_module_order: 4,\n\t\t},\n\t\t// {\n\t\t// \tservice_module_id: 3,\n\t\t// \tservice_module_name: \"学习服务\",\n\t\t// \tservice_id: 25,\n\t\t// \tservice_name: \"课表查询\",\n\t\t// \tservice_type: \"WXAPP\",\n\t\t// \tservice_icon: \"/static/icon/LearningService/ScheduleInquiry.png\",\n\t\t// \tservice_url: \"\",\n\t\t// \twxapp_id: \"wx5da532e7f5b2afaf\",\n\t\t// \tservice_order: 100,\n\t\t// \tservice_module_order: 3,\n\t\t// },\n\t\t{\n\t\t\tservice_module_id: 3,\n\t\t\tservice_module_name: \"综合服务\",\n\t\t\tservice_id: 18,\n\t\t\tservice_name: \"校园网注册\",\n\t\t\tservice_type: \"H5APP\",\n\t\t\tservice_icon: \"/static/icon/ResourceApply/CampusNetwork.png\",\n\t\t\tservice_url: \"https://oauth.henau.edu.cn/app/CNPRS\",\n\t\t\tservice_order: 100,\n\t\t\tservice_module_order: 3,\n\t\t},\n\t\t{\n\t\t\tservice_module_id: 3,\n\t\t\tservice_module_name: \"学习服务\",\n\t\t\tservice_id: 30,\n\t\t\tservice_name: \"电子成绩单\",\n\t\t\tservice_type: \"WXAPP\",\n\t\t\tservice_icon: \"/static/icon/LearningService/Transcript.png\",\n\t\t\tservice_url: \"\",\n\t\t\twxapp_id: \"wx5da532e7f5b2afaf\",\n\t\t\tservice_order: 100,\n\t\t\tservice_module_order: 3,\n\t\t},\n\t\t{\n\t\t\tservice_module_id: 1,\n\t\t\tservice_module_name: \"综合服务\",\n\t\t\tservice_id: 45,\n\t\t\tservice_name: \"校园地图\",\n\t\t\tservice_type: \"H5APP\",\n\t\t\tservice_icon: \"/static/icon/GeneralService/henaumap.svg\",\n\t\t\tservice_url: \"https://henaumap.henau.edu.cn/\",\n\t\t\tservice_order: 100,\n\t\t\tservice_module_order: 1,\n\t\t},\n\t\t{\n\t\t\tservice_module_id: 1,\n\t\t\tservice_module_name: \"综合服务\",\n\t\t\tservice_id: 58,\n\t\t\tservice_name: \"OA办公系统\",\n\t\t\tservice_type: \"H5APP\",\n\t\t\tservice_icon: \"/static/icon/GeneralService/OA.png\",\n\t\t\tservice_url: \"https://oauth.henau.edu.cn/oauth2_qr_connect/login?appid=ndfe16be378bbef3a1&redirect_uri=https%3A%2F%2Foa.henau.edu.cn/sso.php&response_type=code&scope=henauapi_login&state=1_\",\n\t\t\tservice_order: 100,\n\t\t\tservice_module_order: 1,\n\t\t},\n\t]);\n\n\t// 组件服务数据\n\tconst widgetData = ref([{\n\t\t\tservice_id: 1,\n\t\t\tservice_name: \"校园卡付款码\",\n\t\t\tservice_type: \"H5APP\",\n\t\t\tservice_icon: \"/static/icon/GeneralService/CampusCard.png\",\n\t\t\tservice_url: \"https://yktwx.henau.edu.cn/berserker-auth/wechat/token/mp?resultUrl=https%3A%2F%2Fyktwx.henau.edu.cn%2Fplat%2Fpay%3FappId%3D12%26loginFrom%3Dwechat-mp%26synAccessSource%3Dwechat-mp%26nodeId%3D-12\",\n\t\t},\n\t\t// {\n\t\t// \tservice_id: 2,\n\t\t// \tservice_name: \"校园网注册\",\n\t\t// \tservice_type: \"H5APP\",\n\t\t// \tservice_icon: \"/static/icon/ResourceApply/CampusNetwork.png\",\n\t\t// \tservice_url: \"https://oauth.henau.edu.cn/app/CNPRS\",\n\t\t// },\n\t\t{\n\t\t\tservice_id: 2,\n\t\t\tservice_name: \"访客预约\",\n\t\t\tservice_type: \"H5APP\",\n\t\t\tservice_icon: \"/static/icon/GeneralService/Visitor.png\",\n\t\t\tservice_url: \"https://bwcfr.henau.edu.cn/visitor/#/pages/index/index\",\n\t\t},\n\t\t{\n\t\t\tservice_id: 3,\n\t\t\tservice_name: \"失物招领\",\n\t\t\tservice_type: \"H5APP\",\n\t\t\tservice_icon: \"/static/icon/GeneralService/LosingStuff.svg\",\n\t\t\tservice_url: \"https://swzl.henau.edu.cn/swzl/feed/index\",\n\t\t},\n\t\t{\n\t\t\tservice_id: 4,\n\t\t\tservice_name: \"农宝圈\",\n\t\t\tservice_type: \"H5APP\",\n\t\t\tservice_icon: \"/static/icon/GeneralService/HenauMoments.png\",\n\t\t\tservice_url: \"https://moments.henau.edu.cn/#/Index\",\n\t\t},\n\t]);\r\n\t\r\n\t\r\n\t\r\n\t\r\n\t// 新增的农大新生专栏点击事件\r\n\t\tconst goToNewStudentCorner = () => {\r\n\t\t\tuni.switchTab({\r\n\t\t\t\turl: '/pages/NewStudentKnowledge/index/index',\r\n\t\t\t});\r\n\t\t};\n\n\t// 服务点击事件\n\tconst goToService = (item) => {\n\t\tif (item.service_type === \"H5APP\") {\n\t\t\t// 如果是 H5 页面，跳转到 Webview\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/serviceWebView/serviceWebView?webviewUrl=${encodeURIComponent(\n\t\t\t\titem.service_url\n\t\t\t)}`,\n\t\t\t});\n\t\t} else if (item.service_type === \"WXAPP\") {\n\t\t\t// 如果是小程序，跳转到指定的小程序\n\t\t\tuni.navigateToMiniProgram({\n\t\t\t\tappId: item.wxapp_id,\n\t\t\t\tpath: item.service_url,\n\t\t\t\tsuccess(res) {\n\t\t\t\t\tconsole.log(\"小程序跳转成功\", res);\n\t\t\t\t},\n\t\t\t\tfail(err) {\n\t\t\t\t\tconsole.log(\"小程序跳转失败\", err);\n\t\t\t\t},\n\t\t\t});\n\t\t} else {\n\t\t\tconsole.log(\"无法识别的服务类型\");\n\t\t}\n\t};\n\n\t// 更多服务\n\tconst moreService = () => {\n\t\tuni.navigateTo({\n\t\t\turl: \"/pages/microservices/microservices\",\n\t\t});\n\t};\n\t// 分享给朋友和朋友圈\n\tonShareAppMessage(() => {});\n\tonShareTimeline(() => {});\n\n\tonLoad((query) => {\n\t\t// 获取由webview分享页面携带的参数\n\t\tconst webViewUrl = decodeURIComponent(query.webViewUrl);\n\t\t// 获取扫码参数，对于扫描电子通行证进入的场景，使用单独的webview页面处理\n\t\tconst q = decodeURIComponent(query.q); // 获取到二维码原始链接内容\n\t\tconst scancode_time = parseInt(query.scancode_time); // 获取用户扫码时间 UNIX 时间戳\n\t\t// 微信webview在点击右下角图标返回小程序首页时q会是字符串'undefined'，需要进行过滤\n\t\tif (q && q != undefined && q != \"undefined\") {\n\t\t\t// 指定的电子通行证域名列表\n\t\t\tconst acDomains = [\"ac.henau.edu.cn\"];\n\t\t\tconst containsAllowedDomain = (url) => {\n\t\t\t\treturn acDomains.some((domain) => url.includes(domain));\n\t\t\t};\n\t\t\t// 对于电子通行证进行单独处理\n\t\t\tif (containsAllowedDomain(q)) {\n\t\t\t\t// uni.showModal({\n\t\t\t\t// \ttitle: \"提示\",\n\t\t\t\t// \tcontent: q,\n\t\t\t\t// \tshowCancel: true,\n\t\t\t\t// \tsuccess: ({ confirm, cancel }) => {},\n\t\t\t\t// });\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/ac/ac?webviewUrl=${encodeURIComponent(q)}`,\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/serviceWebView/serviceWebView?webviewUrl=${encodeURIComponent(\n\t\t\t\t\tq\n\t\t\t\t)}`,\n\t\t\t\t});\n\t\t\t\t// uni.showModal({\n\t\t\t\t// \ttitle: \"提示\",\n\t\t\t\t// \tcontent: \"none\",\n\t\t\t\t// \tshowCancel: true,\n\t\t\t\t// \tsuccess: ({ confirm, cancel }) => {},\n\t\t\t\t// });\n\t\t\t}\n\t\t}\n\t\t// 处理分享过来的 webViewUrl\n\t\tif (webViewUrl && webViewUrl !== \"undefined\") {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/serviceWebView/serviceWebView?webviewUrl=${encodeURIComponent(\n\t\t\t\twebViewUrl\n\t\t\t)}`,\n\t\t\t});\n\t\t}\n\t});\n</script>\n\n<style>\n\t.container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tmin-height: 100vh;\n\t\t/* 确保容器至少占满视口高度 */\n\t}\n\n\t.content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 15px;\n\t\tmargin-top: 5px;\n\t\tbackground-color: #f8f8f8;\n\t}\n\n\t.grid-container {\n\t\twidth: 100%;\n\t}\n\n\t.grid-item {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: 100%;\n\t}\n\n\t.grid-item-icon {\n\t\twidth: 40px;\n\t\theight: 40px;\n\t\tmargin-bottom: 8px;\n\t\t/* 图标和文本的间距 */\n\t\tobject-fit: contain;\n\t}\n\n\t.grid-item-text {\n\t\tfont-size: 12px;\n\t\t/* 字体大小统一设置为12px */\n\t\tcolor: #000;\n\t\ttext-align: center;\n\t}\n\n\t/* 最后一行不足四个时，自动向左对齐 */\n\t.grid-container>.grid-item:nth-child(4n + 1) {\n\t\t/* 这里是为了避免最后一行空隙，保持左对齐 */\n\t\tmargin-left: 0;\n\t}\n\n\t.service-container {\n\t\t/* 列与列、行与行之间的间距 */\n\t\twidth: 100%;\n\t\tmax-width: 1200px;\n\t\t/* 控制容器最大宽度 */\n\t\tpadding: 15px 0;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 12px;\n\t}\n\n\t.service-list {\n\t\t/* width: 100%; */\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(4, 1fr);\n\t\t/* 每行四列 */\n\t\tgrid-gap: 10px;\n\n\t\t/* box-shadow: rgba(50, 50, 105, 0.15) 0px 2px 5px 0px, rgba(0, 0, 0, 0.05) 0px 1px 1px 0px; */\n\t}\n\n\t.service-title {\n\t\tcolor: #999999;\n\t\tfont-size: 12px;\n\t\tmargin: 0 0 15px 10px;\n\t}\n\n\t.copyright {\n\t\ttext-align: center;\n\t\tmargin-top: auto;\n\t\tmargin-bottom: 30px;\n\t\tcolor: #999999;\n\t}\n\n\t.version-number {\n\t\tcursor: pointer;\n\t\tuser-select: none;\n\t\tfont-size: 12px;\n\t\t/* 禁止选中文本，有助于减少默认交互效果 */\n\t\t-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n\t\t/* 将点击高亮颜色设置为透明，去除点击时的背景色显示 */\n\t}\n\n\t.technical-support {\n\t\tcursor: pointer;\n\t\tuser-select: none;\n\t\tfont-size: 12px;\n\t\t/* 禁止选中文本，有助于减少默认交互效果 */\n\t\t-webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n\t\t/* 将点击高亮颜色设置为透明，去除点击时的背景色显示 */\n\t\tmargin-top: 5px;\n\t}\n\n\t.head-image {\n\t\twidth: 100%;\n\t}\n\n\t.head-bg-img {\n\t\twidth: 100%;\n\t\theight: 150px;\n\t}\n\n\t.widget-container {\n\t\t/* 列与列、行与行之间的间距 */\n\t\twidth: 100%;\n\t\tmax-width: 1200px;\n\t\t/* 控制容器最大宽度 */\n\t\tpadding: 10px 0;\n\t}\n\n\t.widget-list {\n\t\tdisplay: grid;\n\t\tgrid-template-columns: repeat(2, 1fr);\n\t\tgrid-gap: 15px;\n\t}\n\n\t.widget-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\ttext-align: justify;\n\t\tpadding: 15px 10px;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 12px;\n\t}\n\n\t.icon-container {\n\t\twidth: 40%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\n\t.widget-item-icon {\n\t\twidth: 45px;\n\t\theight: 45px;\n\t\t/* 图标和文本的间距 */\n\t\tobject-fit: contain;\n\t}\n\n\t.widget-item-text {\n\t\tfont-size: 14px;\n\t\tcolor: #000;\n\t\ttext-align: center;\n\t\twidth: 60%;\n\t}\r\n\t\r\n\t\r\n\t/* 新增的农大新生专栏按钮样式 */\r\n\t\t.new-student-container {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center; /* Center the button */\r\n\t\t\talign-items: center;\r\n\t\t\theight: 120px; /* Increased height for the container */\r\n\t\t\tmargin-top: 20px; /* Pull it up to overlap head-image */\r\n\t\t\tmargin-bottom: 0px; /* Spacing below the button */\r\n\t\t\tpadding: 0 10px; /* Padding on sides */\r\n\t\t\twidth: 100%;\r\n\t\t\tbox-sizing: border-box; /* Include padding in width */\r\n\t\t}\r\n\t\r\n\t\t.new-student-button {\r\n\t\t\twidth: 110%; \r\n\t\t\tbox-shadow: 0 0 1px;\r\n\t\t\theight: 105%;\r\n\t\t\tbackground: white; /* Gradient background */\r\n\t\t\tborder-radius: 12px; /* More rounded corners */\r\n\t\t\toverflow: hidden; \r\n\t\r\n\t\t\tcursor: pointer;\r\n\t\t\ttransition: transform 0.3s ease, box-shadow 0.3s ease;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\t\r\n\t\t.new-student-button:active {\r\n\t\t\ttransform: scale(0.98); /* Slightly shrink on click */\r\n\t\t\tbox-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);\r\n\t\t}\r\n\t\r\n\t\t.newStu_image {\r\n\t\t\tborder: #000 1px;\r\n\t\t\twidth: 110%;\r\n\t\t\theight: 130%;\r\n\t\t\tobject-fit: cover; /* Cover the button area */\r\n\t\t\tborder-radius: 12px; /* Match button border-radius */\r\n\t\t}\r\n\t\r\n\t\t.new-student-text {\r\n\t\t\tfont-size: 18px; /* Larger font size */\r\n\t\t\tfont-weight: bold;\r\n\t\t\tcolor: #fff; /* White text for contrast */\r\n\t\t\tletter-spacing: 1.5px;\r\n\t\t\ttext-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); /* Text shadow for readability */\r\n\t\t}\r\n\t\n</style>", "import MiniProgramPage from 'D:/新生小程序/microportal-dev-ls/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useBaseStore", "ref", "uni", "onShareAppMessage", "onShareTimeline", "onLoad", "MiniProgramPage"], "mappings": ";;;;;;AAsEAA,6BAAA;AAmBA,UAAA,eAAAC,cAAAA,IAAA;AAAA,MAAA;AAAA,QACA,mBAAA;AAAA,QACA,qBAAA;AAAA,QACA,YAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,aAAA;AAAA,QACA,eAAA;AAAA,QACA,sBAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,mBAAA;AAAA,QACA,qBAAA;AAAA,QACA,YAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,aAAA;AAAA,QACA,eAAA;AAAA,QACA,sBAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,mBAAA;AAAA,QACA,qBAAA;AAAA,QACA,YAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,aAAA;AAAA,QACA,eAAA;AAAA,QACA,sBAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,mBAAA;AAAA,QACA,qBAAA;AAAA,QACA,YAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,aAAA;AAAA,QACA,eAAA;AAAA,QACA,sBAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,mBAAA;AAAA,QACA,qBAAA;AAAA,QACA,YAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,aAAA;AAAA,QACA,eAAA;AAAA,QACA,sBAAA;AAAA,MACA;AAAA,MAEA;AAAA,QACA,mBAAA;AAAA,QACA,qBAAA;AAAA,QACA,YAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,aAAA;AAAA,QACA,eAAA;AAAA,QACA,sBAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,mBAAA;AAAA,QACA,qBAAA;AAAA,QACA,YAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,aAAA;AAAA,QACA,eAAA;AAAA,QACA,sBAAA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAaA;AAAA,QACA,mBAAA;AAAA,QACA,qBAAA;AAAA,QACA,YAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,aAAA;AAAA,QACA,eAAA;AAAA,QACA,sBAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,mBAAA;AAAA,QACA,qBAAA;AAAA,QACA,YAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,aAAA;AAAA,QACA,UAAA;AAAA,QACA,eAAA;AAAA,QACA,sBAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,mBAAA;AAAA,QACA,qBAAA;AAAA,QACA,YAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,aAAA;AAAA,QACA,eAAA;AAAA,QACA,sBAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,mBAAA;AAAA,QACA,qBAAA;AAAA,QACA,YAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,aAAA;AAAA,QACA,eAAA;AAAA,QACA,sBAAA;AAAA,MACA;AAAA,IACA,CAAA;AAGA,UAAA,aAAAA,cAAAA,IAAA;AAAA,MAAA;AAAA,QACA,YAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,aAAA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA;AAAA,QACA,YAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,aAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,YAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,aAAA;AAAA,MACA;AAAA,MACA;AAAA,QACA,YAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,cAAA;AAAA,QACA,aAAA;AAAA,MACA;AAAA,IACA,CAAA;AAMA,UAAA,uBAAA,MAAA;AACAC,oBAAAA,MAAA,UAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAGA,UAAA,cAAA,CAAA,SAAA;AACA,UAAA,KAAA,iBAAA,SAAA;AAEAA,sBAAAA,MAAA,WAAA;AAAA,UACA,KAAA,mDAAA;AAAA,YACA,KAAA;AAAA,UACA,CAAA;AAAA,QACA,CAAA;AAAA,MACA,WAAA,KAAA,iBAAA,SAAA;AAEAA,sBAAAA,MAAA,sBAAA;AAAA,UACA,OAAA,KAAA;AAAA,UACA,MAAA,KAAA;AAAA,UACA,QAAA,KAAA;AACAA,0BAAA,MAAA,MAAA,OAAA,gCAAA,WAAA,GAAA;AAAA,UACA;AAAA,UACA,KAAA,KAAA;AACAA,0BAAA,MAAA,MAAA,OAAA,gCAAA,WAAA,GAAA;AAAA,UACA;AAAA,QACA,CAAA;AAAA,MACA,OAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,WAAA;AAAA,MACA;AAAA,IACA;AAGA,UAAA,cAAA,MAAA;AACAA,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA;AAAA,MACA,CAAA;AAAA,IACA;AAEAC,kBAAA,kBAAA,MAAA;AAAA,IAAA,CAAA;AACAC,kBAAA,gBAAA,MAAA;AAAA,IAAA,CAAA;AAEAC,kBAAA,OAAA,CAAA,UAAA;AAEA,YAAA,aAAA,mBAAA,MAAA,UAAA;AAEA,YAAA,IAAA,mBAAA,MAAA,CAAA;AACA,eAAA,MAAA,aAAA;AAEA,UAAA,KAAA,KAAA,UAAA,KAAA,aAAA;AAEA,cAAA,YAAA,CAAA,iBAAA;AACA,cAAA,wBAAA,CAAA,QAAA;AACA,iBAAA,UAAA,KAAA,CAAA,WAAA,IAAA,SAAA,MAAA,CAAA;AAAA,QACA;AAEA,YAAA,sBAAA,CAAA,GAAA;AAOAH,wBAAAA,MAAA,WAAA;AAAA,YACA,KAAA,2BAAA,mBAAA,CAAA,CAAA;AAAA,UACA,CAAA;AAAA,QACA,OAAA;AACAA,wBAAAA,MAAA,WAAA;AAAA,YACA,KAAA,mDAAA;AAAA,cACA;AAAA,YACA,CAAA;AAAA,UACA,CAAA;AAAA,QAOA;AAAA,MACA;AAEA,UAAA,cAAA,eAAA,aAAA;AACAA,sBAAAA,MAAA,WAAA;AAAA,UACA,KAAA,mDAAA;AAAA,YACA;AAAA,UACA,CAAA;AAAA,QACA,CAAA;AAAA,MACA;AAAA,IACA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;ACnWA,GAAG,WAAWI,SAAe;"}