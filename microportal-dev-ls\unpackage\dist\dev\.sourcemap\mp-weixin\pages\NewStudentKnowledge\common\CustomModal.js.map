{"version": 3, "file": "CustomModal.js", "sources": ["pages/NewStudentKnowledge/common/CustomModal.vue", "../../test/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDov5paw55Sf5bCP56iL5bqPL21pY3JvcG9ydGFsLWRldi1scy9wYWdlcy9OZXdTdHVkZW50S25vd2xlZGdlL2NvbW1vbi9DdXN0b21Nb2RhbC52dWU"], "sourcesContent": ["<template>\n  <!-- 遮罩层 -->\n  <view class=\"custom-modal-mask\" :class=\"{ 'is-visible': visible }\">\n    <!-- 模态框主体 -->\n    <view class=\"custom-modal\" :class=\"{ 'is-active': visible }\">\n      <!-- 标题区域 -->\n      <view class=\"modal-title\" v-if=\"title\">\n        {{ title }}\n      </view>\n      <!-- 内容区域，用 text 并设置 white-space 确保 \\n 换行 -->\n      <view class=\"modal-content\">\n        <text class=\"content-text\" :style=\"{ lineHeight: lineHeight + 'rpx' }\">\n          {{ content }}\n        </text>\n      </view>\n      <!-- 按钮区域 -->\n      <view class=\"modal-btns\" v-if=\"showButton\">\n        <view\n          class=\"modal-btn cancel-btn\"\n          :style=\"{ borderColor: cancelBtnColor, color: cancelBtnColor }\"\n          @click=\"onCancel\"\n          v-if=\"showCancel\"\n        >\n          {{ cancelText }}\n        </view>\n        <view\n          class=\"modal-btn confirm-btn\"\n          :style=\"{ backgroundColor: confirmBtnColor }\"\n          @click=\"onConfirm\"\n        >\n          {{ confirmText }}\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { defineProps, defineEmits } from 'vue';\n\n// 接收的属性\nconst props = defineProps({\n  visible: { // 是否显示模态框\n    type: Boolean,\n    default: false\n  },\n  title: { // 标题\n    type: String,\n    default: ''\n  },\n  content: { // 内容，支持 \\n 换行\n    type: String,\n    default: ''\n  },\n  confirmText: { // 确认按钮文字\n    type: String,\n    default: '确认'\n  },\n  cancelText: { // 取消按钮文字\n    type: String,\n    default: '取消'\n  },\n  showCancel: { // 是否显示取消按钮\n    type: Boolean,\n    default: false\n  },\n  confirmBtnColor: { // 确认按钮颜色\n    type: String,\n    default: '#4CAF50' // 更柔和的绿色\n  },\n  cancelBtnColor: { // 取消按钮颜色（边框和文字）\n    type: String,\n    default: '#607D8B' // 沉稳的灰色\n  },\n  lineHeight: { // 内容行高，用于微调排版\n    type: Number,\n    default: 48 // 调整为更舒适的行高，单位 rpx\n  },\n  showButton: { // 是否显示按钮（极端情况可隐藏，比如纯提示）\n    type: Boolean,\n    default: true\n  }\n});\n\n// 触发的事件\nconst emit = defineEmits(['confirm', 'cancel']);\n\n// 确认按钮逻辑\nconst onConfirm = () => {\n  emit('confirm');\n};\n\n// 取消按钮逻辑\nconst onCancel = () => {\n  emit('cancel');\n};\n</script>\n\n<style scoped>\n/* 遮罩层：全屏、半透明背景 */\n.custom-modal-mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0); /* 初始透明 */\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 999;\n  opacity: 0; /* 初始隐藏 */\n  visibility: hidden; /* 初始不可见 */\n  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;\n}\n\n.custom-modal-mask.is-visible {\n  background-color: rgba(0, 0, 0, 0.6); /* 显示时变深 */\n  opacity: 1;\n  visibility: visible;\n}\n\n/* 模态框主体：居中、白色背景、圆角、阴影 */\n.custom-modal {\n  width: 80%;\n  max-width: 600rpx; /* 限制最大宽度，适配大屏幕 */\n  background-color: #fff;\n  border-radius: 20rpx; /* 增大圆角 */\n  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.15); /* 更柔和的阴影 */\n  overflow: hidden;\n  transform: scale(0.8); /* 初始缩小 */\n  opacity: 0; /* 初始隐藏 */\n  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 0.3s ease-in-out;\n}\n\n.custom-modal.is-active {\n  transform: scale(1); /* 显示时放大 */\n  opacity: 1;\n}\n\n/* 标题样式：居中、加粗、内边距 */\n.modal-title {\n  font-size: 36rpx; /* 增大字体 */\n  font-weight: bold;\n  text-align: center;\n  padding: 40rpx 30rpx; /* 增大内边距 */\n  color: #333; /* 更深的字体颜色 */\n  border-bottom: 1rpx solid #ebebeb; /* 更浅的分割线 */\n}\n\n/* 内容容器：内边距，让 text 排版更舒适 */\n.modal-content {\n  padding: 40rpx 30rpx; /* 增大内边距 */\n  font-size: 30rpx; /* 增大字体 */\n  color: #555; /* 更深的字体颜色 */\n  text-align: center; /* 内容居中 */\n}\n\n/* 关键：让 text 支持 \\n 换行 */\n.content-text {\n  white-space: pre-wrap;\n  word-wrap: break-word;\n}\n\n/* 按钮容器：flex 布局，平分空间（有取消按钮时） */\n.modal-btns {\n  display: flex;\n  border-top: 1rpx solid #ebebeb; /* 更浅的分割线 */\n}\n\n/* 按钮通用样式：flex 占比、文字居中、内边距 */\n.modal-btn {\n  flex: 1;\n  text-align: center;\n  padding: 30rpx 0; /* 增大内边距 */\n  font-size: 32rpx;\n  cursor: pointer; /* 添加手型光标 */\n  transition: background-color 0.2s ease, color 0.2s ease, transform 0.1s ease;\n  border-radius: 0 0 20rpx 20rpx; /* 底部圆角 */\n}\n\n/* 确认按钮样式 */\n.confirm-btn {\n  color: #fff;\n}\n\n/* 取消按钮样式：仅边框和文字颜色，背景透明 */\n.cancel-btn {\n  background-color: transparent;\n  border-right: 1rpx solid #ebebeb; /* 将 border-left 改为 border-right，让确认按钮始终在右侧 */\n  border-radius: 0 0 0 20rpx; /* 左下角圆角 */\n}\n\n/* 按钮点击效果 */\n.modal-btn:active {\n  transform: scale(0.98);\n  opacity: 0.8;\n}\n\n/* 确保当只有一个按钮时，它占据全部宽度并正确圆角 */\n.modal-btns .confirm-btn:only-child {\n    border-radius: 0 0 20rpx 20rpx;\n    border-right: none; /* 移除当只有一个按钮时的右边框 */\n}\n\n</style>\n", "import Component from 'D:/新生小程序/microportal-dev-ls/pages/NewStudentKnowledge/common/CustomModal.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqFA,UAAM,OAAO;AAGb,UAAM,YAAY,MAAM;AACtB,WAAK,SAAS;AAAA,IAChB;AAGA,UAAM,WAAW,MAAM;AACrB,WAAK,QAAQ;AAAA,IACf;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9FA,GAAG,gBAAgB,SAAS;"}