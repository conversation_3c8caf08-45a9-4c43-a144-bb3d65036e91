"use strict";const e=require("../../../common/vendor.js");Math||n();const n=()=>"../common/CustomModal.js",o={__name:"index",setup(n){const o=n=>{console.log(`导航到 ${n} 校区`),e.index.navigateTo({url:`/pages/NewStudentKnowledge/${n}/${n}`})},a=e.ref(!1),t=e.ref('河南农业大学IT工作室隶属于河南农业大学信息化办公室，主要负责校园信息化建设与维护。\n-----------主要职务内容-------------\n前端开发(Web/小程序)\n后端开发\nUI/UX设计\n运维技术\n-----------------------------------\n有意向的同学请关注\n"河南农业大学信息化办公室"公众号\n我们期待你的加入！'),c=()=>{a.value=!0},u=()=>{a.value=!1};return(n,s)=>({a:e.o((e=>o("wenhua"))),b:e.o((e=>o("longzihu"))),c:e.o((e=>n.jumpxuchang())),d:e.o((e=>o("xuchang"))),e:e.o((e=>o("changyong"))),f:e.o(c),g:e.o(u),h:e.p({visible:a.value,title:"河南农业大学IT工作室",content:t.value,confirmText:"好的",showCancel:!1})})}},a=e._export_sfc(o,[["__scopeId","data-v-c7b292ed"]]);wx.createPage(a);
