# 河南农业大学小程序后端服务

这是一个基于Node.js和Express框架开发的后端服务，主要用于河南农业大学小程序的用户认证和日志管理。

## 功能特性

- ✅ **用户认证**: 通过河南农业大学OAuth系统进行用户身份验证
- ✅ **用户日志**: 完整的用户操作日志记录和查询功能
- ✅ **数据库支持**: 使用MySQL数据库存储用户信息和日志
- ✅ **安全防护**: 集成Helmet、CORS、速率限制等安全中间件
- ✅ **错误处理**: 完善的错误处理和日志记录机制

## 技术栈

- **运行环境**: Node.js 16+
- **Web框架**: Express.js
- **数据库**: MySQL 8.0+
- **HTTP客户端**: Axios
- **安全中间件**: Helmet, CORS, Express-rate-limit
- **环境配置**: dotenv

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

复制 `.env.example` 文件为 `.env` 并填写相应配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，填写以下配置：

```env
# 服务器配置
PORT=3000

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=henau_miniprogram

# 河南农业大学OAuth配置
HENAU_APPID=your_appid
HENAU_SECRET=your_secret

# 环境配置
NODE_ENV=development
```

### 3. 初始化数据库

执行SQL脚本创建数据库和表：

```bash
mysql -u root -p < sql/init.sql
```

### 4. 启动服务

开发环境：

```bash
npm run dev
```

生产环境：

```bash
npm start
```

服务启动后，访问 `http://localhost:3000/health` 检查服务状态。

## API接口文档

### 1. 健康检查

**GET** `/health`

检查服务和数据库连接状态。

**响应示例：**

```json
{
  "status": "success",
  "message": "Service is healthy",
  "data": {
    "timestamp": "2024-01-01T00:00:00.000Z",
    "database": "connected",
    "version": "1.0.0"
  }
}
```

### 2. 用户登录认证

**POST** `/api/auth/login`

通过微信小程序code获取用户信息。

**请求参数：**

```json
{
  "code": "微信小程序授权码"
}
```

**响应示例：**

*校内用户（已关联身份）：*

```json
{
  "status": "success",
  "message": "Login successful",
  "data": {
    "henau_openid": "OPENID",
    "mp_openid": "MPOPENID",
    "user_name": "张三",
    "user_number": "123456",
    "user_section": "信息与管理科学学院",
    "user_phone": "13200000001",
    "session_key": "xxxxxxxxxx",
    "user_status": 2,
    "user_id": 1
  }
}
```

*校外用户（未关联身份）：*

```json
{
  "status": "success",
  "message": "Login successful",
  "data": {
    "mp_openid": "MPOPENID",
    "henau_bind_url": "https://xx.xxx.xx/xx/xxx",
    "session_key": "xxxxxxxxxx",
    "user_status": 6,
    "user_id": 2
  }
}
```

**注意：** `user_phone`、`user_nickname`、`user_avatar_url` 字段需要向信息化办公室单独申请权限才会返回。


### 3. 获取用户访问统计

**GET** `/api/users/statistics`

获取用户访问统计信息，包括总用户数、活跃用户数、访问次数等。

**查询参数：**

- `startDate`: 开始日期（默认30天前）
- `endDate`: 结束日期（默认当前时间）

**响应示例：**

```json
{
  "status": "success",
  "message": "User access statistics retrieved successfully",
  "data": {
    "summary": {
      "totalUsers": 150,
      "activeUsers": 45,
      "newUsers": 12,
      "totalVisits": 320,
      "period": {
        "startDate": "2024-01-01T00:00:00.000Z",
        "endDate": "2024-01-31T23:59:59.999Z"
      }
    },
  }
}
```

### 4. 获取问答列表

**GET** `/api/qa`

获取问答列表，支持分类过滤、关键词搜索和分页。

**查询参数：**
- `category`: 问题分类（可选）
- `page`: 页码（默认1）
- `limit`: 每页数量（默认20）
- `keyword`: 搜索关键词（可选）

**响应示例：**
```json
{
  "status": "success",
  "message": "QA list retrieved successfully",
  "data": {
    "list": [
      {
        "id": 1,
        "question": "Q1：学校今年的校历和作息时间是什么样的？",
        "answer": "",
        "image_url": "https://itstudio.henau.edu.cn/image_hosting/uploads/68942481b8bf0_1754539137.jpg",
        "category": "academic",
        "view_count": 156,
        "created_at": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "totalPages": 3
    }
  }
}
```

### 5. 获取问答详情

**GET** `/api/qa/:id`

获取指定问答的详细信息，会自动增加查看次数。

### 6. 获取问答分类

**GET** `/api/qa/categories`

获取所有问答分类及其问题数量。

### 7. 获取热门问答

**GET** `/api/qa/popular`

获取热门问答列表（按查看次数排序）。

**查询参数：**
- `limit`: 返回数量（默认10）

### 8. 搜索问答

**GET** `/api/qa/search`

搜索问答内容。

**查询参数：**
- `keyword`: 搜索关键词（必填）
- `limit`: 返回数量（默认20）

## 数据库结构

### 用户表 (users)


| 字段            | 类型         | 说明                           |
| --------------- | ------------ | ------------------------------ |
| id              | INT          | 主键ID                         |
| henau_openid    | VARCHAR(100) | 用户唯一标识（校外用户为NULL） |
| mp_openid       | VARCHAR(100) | 微信OpenID（必填）             |
| user_name       | VARCHAR(50)  | 用户姓名（校外用户可能为NULL） |
| user_number     | VARCHAR(20)  | 学/工号（校外用户为NULL）      |
| user_section    | VARCHAR(100) | 学院/部门                      |
| user_phone      | VARCHAR(20)  | 联系方式（需要单独申请权限）   |
| user_nickname   | VARCHAR(50)  | 昵称（需要单独申请权限）       |
| user_avatar_url | TEXT         | 头像链接（需要单独申请权限）   |
| user_status     | TINYINT      | 身份状态（0-6，默认6校外用户） |
| henau_bind_url  | TEXT         | 绑定身份链接（校外用户使用）   |
| session_key     | VARCHAR(100) | 会话密钥                       |
| created_at      | TIMESTAMP    | 创建时间                       |
| updated_at      | TIMESTAMP    | 更新时间                       |
| last_login_at   | TIMESTAMP    | 最后登录时间                   |

**用户身份状态说明：**

- 0: 在校本科生
- 1: 在校研究生
- 2: 在校教职工
- 3: 在校后勤/外聘人员
- 4: 已毕业学生
- 5: 中国现代农业联合研究生院学生
- 6: 校外用户

### 问答表 (qa)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | INT | 主键ID |
| question | TEXT | 问题内容 |
| answer | TEXT | 答案内容 |
| image_url | TEXT | 相关图片链接 |
| category | VARCHAR(50) | 问题分类 |
| is_active | BOOLEAN | 是否启用 |
| sort_order | INT | 排序权重 |
| view_count | INT | 查看次数 |
| created_at | TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | 更新时间 |

**问题分类说明：**
- `academic`: 学术教务
- `library`: 图书馆
- `service`: 学生服务
- `network`: 网络服务
- `life`: 校园生活
- `general`: 常见问题

### 日志表 (user_logs)


| 字段          | 类型         | 说明     |
| ------------- | ------------ | -------- |
| id            | BIGINT       | 主键ID   |
| henau_openid  | VARCHAR(100) | 用户标识 |
| action        | VARCHAR(50)  | 操作类型 |
| description   | TEXT         | 操作描述 |
| ip_address    | VARCHAR(45)  | IP地址   |
| user_agent    | TEXT         | 用户代理 |
| request_data  | JSON         | 请求数据 |
| response_data | JSON         | 响应数据 |
| status        | VARCHAR(20)  | 操作状态 |
| error_message | TEXT         | 错误信息 |
| created_at    | TIMESTAMP    | 创建时间 |

## 部署说明

### 生产环境部署

1. 确保服务器已安装Node.js 16+和MySQL 8.0+
2. 克隆代码到服务器
3. 启动项目 ./start.sh
4. 停止项目 ./stop.sh

### Docker部署

```dockerfile
# Dockerfile示例
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

## 安全注意事项

1. **环境变量**: 不要将敏感信息提交到版本控制系统
2. **HTTPS**: 生产环境建议使用HTTPS
3. **速率限制**: 已配置基础速率限制，可根据需要调整
4. **数据库安全**: 使用强密码，限制数据库访问权限
5. **OAuth安全**: 妥善保管APPID和SECRET，定期更换

## 许可证

ISC License
