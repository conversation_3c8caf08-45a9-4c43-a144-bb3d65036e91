{"version": 3, "file": "web-view.js", "sources": ["pages/NewStudentKnowledge/web-view/web-view.vue", "../../test/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvTmV3U3R1ZGVudEtub3dsZWRnZS93ZWItdmlldy93ZWItdmlldy52dWU"], "sourcesContent": ["<template>\n  <view class=\"webview-container\">\n    <!-- web-view组件用于加载外部网页 -->\n    <web-view :src=\"url\" @load=\"onWebviewLoad\" @error=\"onWebviewError\"></web-view>\n  </view>\n</template>\n\n<script setup>\nimport {\n  onLoad\n} from '@dcloudio/uni-app';\nimport {\n  ref\n} from 'vue';\n\nconst url = ref('');\nconst loading = ref(true); // 控制加载提示的显示\n\n// 页面加载生命周期钩子，获取传递过来的URL参数\nonLoad((options) => {\n  if (options.url) {\n    // 对URL进行解码\n    url.value = decodeURIComponent(options.url);\n    console.log('加载外部链接:', url.value);\n  } else {\n    // 如果没有URL参数，可以显示错误信息或返回\n    console.error('未接收到有效的URL参数');\n    uni.showToast({\n      title: '链接无效',\n      icon: 'none'\n    });\n    loading.value = false; // 停止加载提示\n  }\n});\n\n// web-view加载成功事件（注意：此事件在小程序中可能不总是触发，或触发时机较晚）\nconst onWebviewLoad = () => {\n  console.log('Webview加载成功');\n  loading.value = false; // 隐藏加载提示\n};\n\n// web-view加载失败事件\nconst onWebviewError = (e) => {\n  console.error('Webview加载失败:', e.detail);\n  uni.showToast({\n    title: '链接加载失败，请检查网络或稍后重试',\n    icon: 'none',\n    duration: 3000\n  });\n  loading.value = false; // 隐藏加载提示\n};\n\n</script>\n\n<style lang=\"scss\" scoped>\n.webview-container {\n  width: 100vw;\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n}\n\nweb-view {\n  flex: 1; // web-view填充剩余空间\n}\n\n.loading-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(255, 255, 255, 0.9); // 半透明白色背景\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 999; // 确保在web-view之上\n}\n\n.loading-text {\n  font-size: 32rpx;\n  color: #666;\n}\n</style>\n", "import MiniProgramPage from 'D:/新生小程序/microportal-dev-ls/pages/NewStudentKnowledge/web-view/web-view.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onLoad", "uni"], "mappings": ";;;;;AAeA,UAAM,MAAMA,cAAAA,IAAI,EAAE;AAClB,UAAM,UAAUA,cAAAA,IAAI,IAAI;AAGxBC,kBAAM,OAAC,CAAC,YAAY;AAClB,UAAI,QAAQ,KAAK;AAEf,YAAI,QAAQ,mBAAmB,QAAQ,GAAG;AAC1CC,sBAAY,MAAA,MAAA,OAAA,yDAAA,WAAW,IAAI,KAAK;AAAA,MACpC,OAAS;AAELA,sBAAAA,8EAAc,cAAc;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD,gBAAQ,QAAQ;AAAA,MACjB;AAAA,IACH,CAAC;AAGD,UAAM,gBAAgB,MAAM;AAC1BA,oBAAAA,MAAY,MAAA,OAAA,yDAAA,aAAa;AACzB,cAAQ,QAAQ;AAAA,IAClB;AAGA,UAAM,iBAAiB,CAAC,MAAM;AAC5BA,oBAAc,MAAA,MAAA,SAAA,yDAAA,gBAAgB,EAAE,MAAM;AACtCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,QACN,UAAU;AAAA,MACd,CAAG;AACD,cAAQ,QAAQ;AAAA,IAClB;;;;;;;;;;;ACjDA,GAAG,WAAW,eAAe;"}