#!/bin/bash

# 应用名称（需与启动脚本中的名称一致）
APP_NAME="silent-server"

# 检查PM2是否安装
if ! command -v pm2 &> /dev/null; then
    echo "错误: 未检测到PM2，请先安装PM2: npm install pm2 -g"
    exit 1
fi

# 检查应用是否在运行
pm2 describe $APP_NAME &> /dev/null
if [ $? -ne 0 ]; then
    echo "应用 $APP_NAME 未在运行"
    exit 0
fi

# 停止应用
echo "正在停止应用 $APP_NAME..."
pm2 stop $APP_NAME &> /dev/null

# 从PM2列表中删除
pm2 delete $APP_NAME &> /dev/null

echo "应用已成功停止"
