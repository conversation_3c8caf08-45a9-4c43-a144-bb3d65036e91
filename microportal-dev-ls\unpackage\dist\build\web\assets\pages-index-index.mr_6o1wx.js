import{d as e,r as i,n as c,c as s,w as r,i as n,o as a,a as t,b as o,e as _,F as d,f as u,g as l,h as v,t as p}from"./index-DeHPfleJ.js";import{_ as m,o as g,a as h,b as f}from"./_plugin-vue_export-helper.DFaavnJe.js";const w=e("base",(()=>{const e=i(0);return{miniProjectName:"河南农大微门户",count:e,increment:function(){e.value++}}})),P=m({__name:"index",setup(e){w();const m=i([{service_module_id:1,service_module_name:"综合服务",service_id:2,service_name:"本科生请假",service_type:"H5APP",service_icon:"/static/icon/GeneralService/UndergraduateLeave.png",service_url:"https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxf40cdbdcc58c583e&redirect_uri=https%3a%2f%2fstudqj.henau.edu.cn&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect",service_order:100,service_module_order:1},{service_module_id:1,service_module_name:"综合服务",service_id:4,service_name:"实验室管理",service_type:"H5APP",service_icon:"/static/icon/GeneralService/Laboratory.png",service_url:"https://oauth.henau.edu.cn/oauth2_qr_connect/login?appid=nd35b284cce2d94f0d&redirect_uri=https%3a%2f%2fsysxxh.henau.edu.cn%2foauthlogin.aspx&response_type=code&scope=henauapi_login&state=app",service_order:100,service_module_order:1},{service_module_id:1,service_module_name:"综合服务",service_id:37,service_name:"校园服务电话",service_type:"H5APP",service_icon:"/static/icon/GeneralService/Telephone.png",service_url:"https://microservices.henau.edu.cn/henauwfw/#/Telephone",service_order:100,service_module_order:1},{service_module_id:1,service_module_name:"综合服务",service_id:43,service_name:"校内业务用表",service_type:"H5APP",service_icon:"/static/icon/GeneralService/BusinessForm.png",service_url:"https://microservices.henau.edu.cn/henauwfw/#/BusinessForm",service_order:100,service_module_order:1},{service_module_id:5,service_module_name:"建议反馈",service_id:34,service_name:"灵感小站",service_type:"H5APP",service_icon:"/static/icon/SuggestionFeedback/InspirationStation.png",service_url:"https://microservices.henau.edu.cn/henauwfw/#/SubmitFeedback",service_order:100,service_module_order:5},{service_module_id:1,service_module_name:"综合服务",service_id:57,service_name:"VPN登录",service_type:"H5APP",service_icon:"/static/icon/GeneralService/VPNLogin.png",service_url:"https://vpn2.henau.edu.cn/portal/",service_order:100,service_module_order:1},{service_module_id:4,service_module_name:"我的信息",service_id:38,service_name:"我的消息",service_type:"H5APP",service_icon:"/static/icon/GeneralService/MyMessage.png",service_url:"https://microservices.henau.edu.cn/henauwfw/#/MyMessage",service_order:100,service_module_order:4},{service_module_id:3,service_module_name:"综合服务",service_id:18,service_name:"校园网注册",service_type:"H5APP",service_icon:"/static/icon/ResourceApply/CampusNetwork.png",service_url:"https://oauth.henau.edu.cn/app/CNPRS",service_order:100,service_module_order:3},{service_module_id:3,service_module_name:"学习服务",service_id:30,service_name:"电子成绩单",service_type:"WXAPP",service_icon:"/static/icon/LearningService/Transcript.png",service_url:"",wxapp_id:"wx5da532e7f5b2afaf",service_order:100,service_module_order:3},{service_module_id:1,service_module_name:"综合服务",service_id:45,service_name:"校园地图",service_type:"H5APP",service_icon:"/static/icon/GeneralService/henaumap.svg",service_url:"https://henaumap.henau.edu.cn/",service_order:100,service_module_order:1},{service_module_id:1,service_module_name:"综合服务",service_id:58,service_name:"OA办公系统",service_type:"H5APP",service_icon:"/static/icon/GeneralService/OA.png",service_url:"https://oauth.henau.edu.cn/oauth2_qr_connect/login?appid=ndfe16be378bbef3a1&redirect_uri=https%3A%2F%2Foa.henau.edu.cn/sso.php&response_type=code&scope=henauapi_login&state=1_",service_order:100,service_module_order:1}]),P=i([{service_id:1,service_name:"校园卡付款码",service_type:"H5APP",service_icon:"/static/icon/GeneralService/CampusCard.png",service_url:"https://yktwx.henau.edu.cn/berserker-auth/wechat/token/mp?resultUrl=https%3A%2F%2Fyktwx.henau.edu.cn%2Fplat%2Fpay%3FappId%3D12%26loginFrom%3Dwechat-mp%26synAccessSource%3Dwechat-mp%26nodeId%3D-12"},{service_id:2,service_name:"访客预约",service_type:"H5APP",service_icon:"/static/icon/GeneralService/Visitor.png",service_url:"https://bwcfr.henau.edu.cn/visitor/#/pages/index/index"},{service_id:3,service_name:"失物招领",service_type:"H5APP",service_icon:"/static/icon/GeneralService/LosingStuff.svg",service_url:"https://swzl.henau.edu.cn/swzl/feed/index"},{service_id:4,service_name:"农宝圈",service_type:"H5APP",service_icon:"/static/icon/GeneralService/HenauMoments.png",service_url:"https://moments.henau.edu.cn/#/Index"}]),b=e=>{"H5APP"===e.service_type?c({url:`/pages/serviceWebView/serviceWebView?webviewUrl=${encodeURIComponent(e.service_url)}`}):"WXAPP"===e.service_type?uni.navigateToMiniProgram({appId:e.wxapp_id,path:e.service_url,success(e){console.log("小程序跳转成功",e)},fail(e){console.log("小程序跳转失败",e)}}):console.log("无法识别的服务类型")};return g((()=>{})),h((()=>{})),f((e=>{const i=decodeURIComponent(e.webViewUrl),s=decodeURIComponent(e.q);if(parseInt(e.scancode_time),s&&null!=s&&"undefined"!=s){const e=["ac.henau.edu.cn"];(i=>e.some((e=>i.includes(e))))(s)?c({url:`/pages/ac/ac?webviewUrl=${encodeURIComponent(s)}`}):c({url:`/pages/serviceWebView/serviceWebView?webviewUrl=${encodeURIComponent(s)}`})}i&&"undefined"!==i&&c({url:`/pages/serviceWebView/serviceWebView?webviewUrl=${encodeURIComponent(i)}`})})),(e,i)=>{const g=l,h=n,f=v;return a(),s(h,{class:"container"},{default:r((()=>[t(h,{class:"head-image"},{default:r((()=>[t(g,{src:"/assets/bgimg-CXyJA99e.jpg",class:"head-bg-img",mode:"aspectFill"})])),_:1}),t(h,{class:"grid-container new-student-container"},{default:r((()=>[t(h,{class:"new-student-button",onClick:i[0]||(i[0]=e=>{c({url:`/pages/serviceWebView/serviceWebView?webviewUrl=${encodeURIComponent("http://*************:4000/")}`})})},{default:r((()=>[t(g,{class:"newStu_image",src:"https://itstudio.henau.edu.cn/image_hosting/uploads/6895a7d7c4d2d_1754638295.png"})])),_:1})])),_:1}),t(h,{class:"content"},{default:r((()=>[t(h,{class:"grid-container card-section"},{default:r((()=>[t(h,{class:"widget-container"},{default:r((()=>[t(h,{class:"widget-list"},{default:r((()=>[(a(!0),o(d,null,_(P.value,((e,i)=>(a(),s(h,{class:"widget-item",key:i,onClick:i=>b(e)},{default:r((()=>[t(h,{class:"icon-container"},{default:r((()=>[t(g,{src:e.service_icon||"/static/logo.png",class:"widget-item-icon",mode:"aspectFit"},null,8,["src"])])),_:2},1024),t(f,{class:"widget-item-text"},{default:r((()=>[u(p(e.service_name),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1})])),_:1})])),_:1}),t(h,{class:"grid-container card-section"},{default:r((()=>[t(h,{class:"service-container"},{default:r((()=>[t(h,{class:"service-title"},{default:r((()=>[u("校园服务")])),_:1}),t(h,{class:"service-list"},{default:r((()=>[(a(!0),o(d,null,_(m.value,((e,i)=>(a(),s(h,{class:"grid-item",key:i,onClick:i=>b(e)},{default:r((()=>[t(g,{src:e.service_icon||"/static/logo.png",class:"grid-item-icon",mode:"aspectFit"},null,8,["src"]),t(f,{class:"grid-item-text"},{default:r((()=>[u(p(e.service_name),1)])),_:2},1024)])),_:2},1032,["onClick"])))),128)),t(h,{class:"grid-item",onClick:i[1]||(i[1]=e=>{c({url:"/pages/microservices/microservices"})})},{default:r((()=>[t(g,{src:"/static/icon/more-app.png",class:"grid-item-icon",mode:"aspectFit"},null,8,["src"]),t(f,{class:"grid-item-text"},{default:r((()=>[u("更多服务")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),t(h,{class:"copyright"},{default:r((()=>[t(h,{class:"version-number"},{default:r((()=>[u("MicroService v2.4.2")])),_:1}),t(h,{class:"technical-support"},{default:r((()=>[u("技术支持：河南农业大学IT工作室")])),_:1})])),_:1})])),_:1})}}},[["__scopeId","data-v-0ce316bb"]]);export{P as default};
