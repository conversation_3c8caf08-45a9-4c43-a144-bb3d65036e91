{
	"pages": [
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "农大微门户"
			}
		},
		{
			"path": "pages/microservices/microservices",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/serviceWebView/serviceWebView",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/telephone/telephone",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/businessForm/businessForm",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/henaumap/henaumap",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/studqj/studqj",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/sysxxh/sysxxh",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/oauthAppCNPRS/oauthAppCNPRS",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/myMessage/myMessage",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/yktwx/yktwx",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/ac/ac",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		{
			"path": "pages/visitorReservation/visitorReservation",
			"style": {
				"navigationBarTitleText": ""
			}
		},
		// 新生小程序界面配置-----------------------------
		{
			"path": "pages/NewStudentKnowledge/index/index",
			"style": {
				"navigationBarTitleText": "新起点"
			}
		},
		{
			"path": "pages/NewStudentKnowledge/navigation/index",
			"style": {
				"navigationBarTitleText": "指南"
			}
		},
		{
			"path": "pages/NewStudentKnowledge/map/index",
			"style": {
				"navigationBarTitleText": "校园地图"
			}
		},
		{
			"path": "pages/NewStudentKnowledge/comment/index",
			"style": {
				"navigationBarTitleText": "Q & A"
			}
		},
		{
			"path": "pages/NewStudentKnowledge/landscape/landscape",
			"style": {
				"navigationBarTitleText": "农大风采"
			}
		},
		{
			"path": "pages/NewStudentKnowledge/detail/detail",
			"style": {
				"navigationBarTitleText": "图片详情"
			}
		},
		{
			"path": "pages/NewStudentKnowledge/xuchang/xuchang",
			"style": {
				"navigationBarTitleText": "许昌校区知识"
			}
		},
		{
			"path": "pages/NewStudentKnowledge/longzihu/longzihu",
			"style": {
				"navigationBarTitleText": "龙子湖校区知识"
			}
		},
		{
			"path": "pages/NewStudentKnowledge/wenhua/wenhua",
			"style": {
				"navigationBarTitleText": "文化路校区知识"
			}
		},
		{
			"path": "pages/NewStudentKnowledge/changyong/changyong",
			"style": {
				"navigationBarTitleText": "常用软件"
			}
		},
		{
			"path": "pages/NewStudentKnowledge/web-view/web-view",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom"
			}
		}
		// 新生小程序配置------------------------------
	],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "农大微门户",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"tabBar": {
		"color": "#999999",
		"selectedColor": "#2E7D32",
		"backgroundColor": "#ffffff",
		"borderStyle": "black",
		"list": [
			// 新生小程序底部-------------------------------------
			{
				"pagePath": "pages/NewStudentKnowledge/index/index",
				"iconPath": "/static/NewStudentKnowledge/icons/home.png",  // 统一调整为含NewStudentKnowledge的路径
				"selectedIconPath": "/static/NewStudentKnowledge/icon-select/home.png",
				"text": "新起点"
			},
			{
				"pagePath": "pages/NewStudentKnowledge/navigation/index",
				"iconPath": "/static/NewStudentKnowledge/icons/nav.png",
				"selectedIconPath": "/static/NewStudentKnowledge/icon-select/nav.png",
				"text": "指南"
			},
			{
				"pagePath": "pages/NewStudentKnowledge/map/index",
				"iconPath": "/static/NewStudentKnowledge/icons/map.png",
				"selectedIconPath": "/static/NewStudentKnowledge/icon-select/map.png",
				"text": "地图"
			},
			{
				"pagePath": "pages/NewStudentKnowledge/comment/index",
				"iconPath": "/static/NewStudentKnowledge/icons/comment.png",
				"selectedIconPath": "/static/NewStudentKnowledge/icon-select/comment.png",
				"text": "Q&A"
			}
			// 新生小程序底部----------------------------------
		]
	},
	"uniIdRouter": {},
	"condition": {
		"current": 0,
		"list": [
			{
				"name": "",
				"path": "",
				"query": ""
			}
		]
	}
}