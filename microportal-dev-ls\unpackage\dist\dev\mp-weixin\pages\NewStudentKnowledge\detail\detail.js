"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  __name: "detail",
  setup(__props) {
    const imageUrl = common_vendor.ref("");
    const scaleValue = common_vendor.ref(1);
    common_vendor.onLoad((options) => {
      if (options.imageUrl) {
        imageUrl.value = decodeURIComponent(options.imageUrl);
      }
    });
    const onImageLoad = () => {
      scaleValue.value = 1;
    };
    const closeDetailPage = () => {
      common_vendor.index.navigateBack();
    };
    const resetScaleAndClose = () => {
      scaleValue.value = 1;
      common_vendor.index.navigateBack();
    };
    return (_ctx, _cache) => {
      return {
        a: imageUrl.value,
        b: common_vendor.o(onImageLoad),
        c: scaleValue.value,
        d: common_vendor.o(resetScaleAndClose),
        e: common_vendor.o(closeDetailPage)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-466d9e6e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/NewStudentKnowledge/detail/detail.js.map
