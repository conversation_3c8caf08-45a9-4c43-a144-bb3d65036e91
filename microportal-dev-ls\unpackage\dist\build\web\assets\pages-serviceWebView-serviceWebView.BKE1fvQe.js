import{r as e,p as s,q as a,c as o,w as r,j as n,o as t,a as c,k as i,l,m as p}from"./index-DeHPfleJ.js";import{_ as d}from"./home.LEWUQR7v.js";import{_ as u,c as m,d as w,e as _,o as v,a as f}from"./_plugin-vue_export-helper.DFaavnJe.js";const x=u({__name:"serviceWebView",setup(u){const x=e("");s((()=>{const e=a(),s=e[e.length-1].options;s.webviewUrl&&(x.value=decodeURIComponent(s.webviewUrl))})),m((()=>{})),w((()=>{})),_((e=>{}));return v((e=>{const s=e.webViewUrl;return{path:`/pages/index/index?webViewUrl=${encodeURIComponent(s)}`}})),f((()=>{})),(e,s)=>{const a=l,u=p,m=n;return t(),o(m,{src:x.value},{default:r((()=>[c(u,{class:"close-view",onClick:s[0]||(s[0]=e=>{i({url:"/pages/index/index"})})},{default:r((()=>[c(a,{class:"close-icon",src:d})])),_:1})])),_:1},8,["src"])}}},[["__scopeId","data-v-aab70653"]]);export{x as default};
