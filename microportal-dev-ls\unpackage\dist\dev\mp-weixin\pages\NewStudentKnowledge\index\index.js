"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Math) {
  CustomModal();
}
const CustomModal = () => "../common/CustomModal.js";
const backUrl = "http://localhost";
const fullIntroText = "踏入新征程，开启新篇章，这里是你梦想的起点！在这里，你将收获知识的力量，结识志同道合的朋友，探索无限可能的未来。河南农业大学将为你提供广阔的学习平台，丰富的实践机会，优秀的师资团队，完善的校园设施。无论你来自哪里，无论你的专业是什么，这里都将成为你人生中最重要的成长阶段。让我们一起在这片充满希望的土地上，书写属于你的青春华章，追求学术的真理，培养创新的思维，锻炼实践的能力，成就更好的自己！愿你在这里度过充实而美好的大学时光，收获知识、友谊和成长！";
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const bannerList = common_vendor.ref([
      { src: "https://itstudio.henau.edu.cn/image_hosting/uploads/68931460c8aeb_1754469472.png", alt: "校园风光" },
      { src: "https://itstudio.henau.edu.cn/image_hosting/uploads/6893147e19cdd_1754469502.png", alt: "新生报到" },
      { src: "https://itstudio.henau.edu.cn/image_hosting/uploads/6893149faf7f0_1754469535.png", alt: "教学活动" }
    ]);
    const introText = common_vendor.ref("");
    const introTypingDone = common_vendor.ref(false);
    const introIntervalId = common_vendor.ref(null);
    const showFirework = common_vendor.ref(false);
    const fireworks = common_vendor.ref([]);
    const showStatsModal = common_vendor.ref(false);
    const statsData = common_vendor.ref({ summary: { totalVisits: 0 } });
    common_vendor.ref(0);
    const startIntroTyping = () => {
      let i = 0;
      if (introIntervalId.value) {
        clearInterval(introIntervalId.value);
      }
      introIntervalId.value = setInterval(() => {
        if (i < fullIntroText.length) {
          introText.value += fullIntroText[i];
          i++;
        } else {
          clearInterval(introIntervalId.value);
          introTypingDone.value = true;
          introIntervalId.value = null;
        }
      }, 25);
    };
    const handleSwiperChange = (e) => {
    };
    const handleImageError = (index) => {
      bannerList.value[index].src = "https://itstudio.henau.edu.cn/image_hosting/uploads/689314c7a5ee4_1754469575.png";
    };
    common_vendor.onShareAppMessage(() => {
      return {
        title: "河南农业大学新生指南中心",
        // 分享卡的标题
        path: "/pages/index/index",
        // 分享后用户点击进入的页面路径，请确保路径正确
        imageUrl: bannerList.value[0].src
        // 分享卡上显示的图片，这里使用第一张轮播图
      };
    });
    const isModalShow = common_vendor.ref(false);
    const modalContent = common_vendor.ref(`河南农业大学IT工作室隶属于学校信息化办公室，主要负责校园信息化建设与维护。

招募方向：
- 前端开发(Web/小程序)
- 后端开发
- UI/UX设计
- 运维技术

有意向的同学请关注"河南农业大学信息化办公室"公众号了解详情！`);
    const openModal = () => {
      isModalShow.value = true;
    };
    const closeModal = () => {
      isModalShow.value = false;
    };
    const handleLogin = () => {
      common_vendor.index.login({
        provider: "weixin",
        // 指定微信登录
        success(res) {
          if (res.code) {
            common_vendor.index.request({
              url: `${backUrl}:3000/api/auth/login`,
              // 您的后端登录接口
              method: "POST",
              data: {
                code: res.code
              },
              success(res2) {
                common_vendor.index.__f__("log", "at pages/NewStudentKnowledge/index/index.vue:205", "登录成功！", res2.data);
              },
              fail(err) {
                common_vendor.index.__f__("error", "at pages/NewStudentKnowledge/index/index.vue:208", "登录请求失败：", err);
              }
            });
          } else {
            common_vendor.index.__f__("log", "at pages/NewStudentKnowledge/index/index.vue:212", "uni.login 失败！" + res.errMsg);
          }
        },
        fail(err) {
          common_vendor.index.__f__("error", "at pages/NewStudentKnowledge/index/index.vue:218", "uni.login 调用失败：", err);
        }
      });
    };
    const handlePageTap = () => {
      if (!introTypingDone.value && introIntervalId.value !== null) {
        clearInterval(introIntervalId.value);
        introText.value = fullIntroText;
        introTypingDone.value = true;
        introIntervalId.value = null;
      }
    };
    const goBack = () => {
      common_vendor.index.navigateTo({
        url: "/pages/index/index"
      });
    };
    common_vendor.onLoad(() => {
      common_vendor.index.__f__("log", "at pages/NewStudentKnowledge/index/index.vue:288", "页面加载");
      startIntroTyping();
      handleLogin();
    });
    common_vendor.onShow(() => {
      common_vendor.index.__f__("log", "at pages/NewStudentKnowledge/index/index.vue:297", "页面显示");
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: common_vendor.f(bannerList.value, (item, index, i0) => {
          return {
            a: item.src,
            b: common_vendor.o(($event) => handleImageError(index), index),
            c: index
          };
        }),
        c: common_vendor.o(handleSwiperChange),
        d: common_vendor.t(introText.value),
        e: !introTypingDone.value
      }, !introTypingDone.value ? {} : {}, {
        f: showFirework.value
      }, showFirework.value ? {
        g: common_vendor.f(fireworks.value, (firework, index, i0) => {
          return {
            a: index,
            b: common_vendor.s(firework.style)
          };
        }),
        h: common_vendor.o((...args) => _ctx.hideFirework && _ctx.hideFirework(...args))
      } : {}, {
        i: showStatsModal.value
      }, showStatsModal.value ? common_vendor.e({
        j: statsData.value.summary
      }, statsData.value.summary ? {
        k: common_vendor.t(statsData.value.summary.totalVisits)
      } : {}, {
        l: common_vendor.o(($event) => showStatsModal.value = false),
        m: common_vendor.o(() => {
        }),
        n: common_vendor.o(($event) => showStatsModal.value = false)
      }) : {}, {
        o: introTypingDone.value
      }, introTypingDone.value ? common_vendor.e({
        p: common_vendor.o(openModal),
        q: common_vendor.o(closeModal),
        r: common_vendor.p({
          visible: isModalShow.value,
          title: "河南农业大学IT工作室",
          content: modalContent.value,
          confirmText: "好的",
          showCancel: false
        }),
        s: statsData.value.summary
      }, statsData.value.summary ? {
        t: common_vendor.t(statsData.value.summary.totalVisits)
      } : {}) : {}, {
        v: common_vendor.o(handlePageTap)
      });
    };
  }
};
_sfc_main.__runtimeHooks = 2;
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/NewStudentKnowledge/index/index.js.map
