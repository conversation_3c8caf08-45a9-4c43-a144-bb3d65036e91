"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  __name: "wenhua",
  setup(__props) {
    const readingProgress = common_vendor.ref(0);
    let totalScrollHeight = 0;
    common_vendor.onReady(() => {
      common_vendor.index.createSelectorQuery().select(".main-scroll-view").boundingClientRect((rect) => {
        if (rect) {
          common_vendor.index.createSelectorQuery().select(".content-wrapper").boundingClientRect((contentRect) => {
            if (contentRect) {
              totalScrollHeight = contentRect.height - common_vendor.index.getSystemInfoSync().windowHeight;
              if (totalScrollHeight < 0)
                totalScrollHeight = 0;
            }
          }).exec();
        }
      }).exec();
    });
    common_vendor.onPageScroll((e) => {
      if (totalScrollHeight > 0) {
        readingProgress.value = e.scrollTop / totalScrollHeight * 100;
        if (readingProgress.value > 100)
          readingProgress.value = 100;
      } else {
        readingProgress.value = 0;
      }
    });
    const scrollToTop = () => {
      common_vendor.index.pageScrollTo({
        scrollTop: 0,
        duration: 300
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => _ctx.openLink("https://mp.weixin.qq.com/s/Ef2fh9XbITPCz5APQn4jlw")),
        b: common_vendor.o(($event) => _ctx.openLink("https://mp.weixin.qq.com/s/oBt5oDxPTZ3KbydzbsxXIA?scene=1")),
        c: common_vendor.t(Math.round(readingProgress.value)),
        d: common_vendor.o(scrollToTop)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d2414581"]]);
_sfc_main.__runtimeHooks = 1;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/NewStudentKnowledge/wenhua/wenhua.js.map
