"use strict";const e=require("../../common/vendor.js"),n=require("../../common/assets.js"),o={__name:"serviceWebView",setup(o){const r=e.ref("");e.onMounted((()=>{const e=getCurrentPages(),n=e[e.length-1].options;n.webviewUrl&&(r.value=decodeURIComponent(n.webviewUrl))})),e.onHide((()=>{})),e.onUnload((()=>{})),e.onBackPress((e=>{}));return e.onShareAppMessage((e=>{const n=e.webViewUrl;return{path:`/pages/index/index?webViewUrl=${encodeURIComponent(n)}`}})),e.onShareTimeline((()=>{})),(o,s)=>({a:n._imports_0,b:e.o((n=>{e.index.reLaunch({url:"/pages/index/index"})})),c:r.value})},__runtimeHooks:6};wx.createPage(o);
