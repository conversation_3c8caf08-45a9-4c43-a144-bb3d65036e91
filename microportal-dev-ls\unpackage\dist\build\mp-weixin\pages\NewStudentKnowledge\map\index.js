"use strict";const e=require("../../../common/vendor.js");Math||n();const n=()=>"../common/CustomModal.js",t={__name:"index",setup(n){const t=e.ref([{src:"https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377c0b52_1754649463.jpg",name:"龙子湖校区",description:"主校区，位于郑州市龙子湖高校园区"},{src:"https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377cd362_1754649463.png",name:"文化路校区",description:"百年老校区，省级文物保护单位"},{src:"https://itstudio.henau.edu.cn/image_hosting/uploads/6895d377a3566_1754649463.jpg",name:"许昌校区",description:"新建校区，现代化教学设施"}]),a=e.ref(!1),o=()=>{a.value=!1},s=()=>{e.index.setClipboardData({data:"https://www.720yun.com/vr/315z05drknk?s=332068",success:function(){a.value=!0},fail:function(){e.index.showToast({title:"复制失败，请手动复制",icon:"none"})}})},i=()=>{e.index.navigateTo({url:"/pages/NewStudentKnowledge/landscape/landscape"})},d=e.ref(!1),c=e.ref('河南农业大学IT工作室隶属于河南农业大学信息化办公室，主要负责校园信息化建设与维护。\n-----------主要职务内容-------------\n前端开发(Web/小程序)\n后端开发\nUI/UX设计\n运维技术\n-----------------------------------\n有意向的同学请关注\n"河南农业大学信息化办公室"公众号\n我们期待你的加入！'),u=()=>{d.value=!0},r=()=>{d.value=!1};return(n,l)=>e.e({a:e.f(t.value,((n,t,a)=>({a:n.src,b:e.t(n.name),c:e.t(n.description),d:t,e:e.o((t=>{return a=n.src,void e.index.navigateTo({url:`/pages/NewStudentKnowledge/detail/detail?imageUrl=${encodeURIComponent(a)}`});var a}),t)}))),b:e.o(s),c:e.o(i),d:a.value},a.value?{e:e.o(o),f:e.o((()=>{})),g:e.o(o)}:{},{h:e.o(u),i:e.o(r),j:e.p({visible:d.value,title:"河南农业大学IT工作室",content:c.value,confirmText:"好的",showCancel:!1})})}},a=e._export_sfc(t,[["__scopeId","data-v-496aeaaf"]]);wx.createPage(a);
