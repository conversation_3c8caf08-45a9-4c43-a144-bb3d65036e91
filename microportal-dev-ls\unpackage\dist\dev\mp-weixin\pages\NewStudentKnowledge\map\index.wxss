/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-e4150081 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 50%, #fff0f5 100%);
  padding: 30rpx;
  box-sizing: border-box;
}
.content-wrapper.data-v-e4150081 {
  background: transparent;
  border-radius: 30rpx;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.08);
  padding: 40rpx;
  -webkit-backdrop-filter: blur(20rpx);
          backdrop-filter: blur(20rpx);
}
.header-container.data-v-e4150081 {
  text-align: center;
  margin-bottom: 60rpx;
}
.header-container .header-title.data-v-e4150081 {
  font-size: 48rpx;
  font-weight: 800;
  background: linear-gradient(45deg, #2c5364, #203a43);
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 20rpx;
  display: block;
  -webkit-text-stroke: 1rpx #fff;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}
.header-container .header-subtitle.data-v-e4150081 {
  font-size: 32rpx;
  color: #000000;
  margin-bottom: 10rpx;
  display: block;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.header-container .sub-text.data-v-e4150081 {
  font-size: 24rpx;
  color: #555500;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.images-display-container.data-v-e4150081 {
  margin-top: 40rpx;
}
.campus-image-item.data-v-e4150081 {
  margin-bottom: 40rpx;
  border-radius: 20rpx;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.1);
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
}
.campus-image-item.data-v-e4150081:hover {
  transform: translateY(-6rpx);
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.1);
}
.campus-image-item .image-wrapper.data-v-e4150081 {
  position: relative;
  overflow: hidden;
}
.campus-image-item .image-wrapper .campus-image.data-v-e4150081 {
  width: 100%;
  height: 360rpx;
  transition: transform 0.3s ease;
}
.campus-image-item .campus-info.data-v-e4150081 {
  background: rgba(0, 0, 0, 0.5);
  padding: 30rpx;
  -webkit-backdrop-filter: blur(5rpx);
          backdrop-filter: blur(5rpx);
}
.campus-image-item .campus-info .campus-name.data-v-e4150081 {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 10rpx;
  display: block;
}
.campus-image-item .campus-info .campus-desc.data-v-e4150081 {
  font-size: 28rpx;
  color: #ddd;
  line-height: 1.5;
  display: block;
}
.bottom1.data-v-e4150081 {
  font-weight: 1000;
  margin-top: 20px;
  margin-left: 90px;
}
.buttons-container.data-v-e4150081 {
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
  gap: 30rpx;
  flex-wrap: wrap;
}
.action-button.data-v-e4150081 {
  padding: 25rpx 30rpx;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  border: none;
}
.action-button.data-v-e4150081:after {
  border: none;
}
.action-button .button-text.data-v-e4150081 {
  font-size: 22rpx;
  font-weight: 600;
  color: #fff;
}
.action-button.data-v-e4150081:active {
  transform: scale(0.98);
}
.map-button.data-v-e4150081 {
  background: linear-gradient(45deg, #00b09b, #96c93d);
}
.photo-button.data-v-e4150081 {
  background: linear-gradient(45deg, #ff6b6b, #f06595);
}
@media screen and (min-width: 768px) {
.images-display-container.data-v-e4150081 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30rpx;
}
.campus-image-item.data-v-e4150081 {
    margin-bottom: 0;
}
}
/* 自定义美化弹窗样式 */
.custom-modal-overlay.data-v-e4150081 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn-e4150081 0.3s ease-out;
}
.custom-modal-content.data-v-e4150081 {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 50rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 90%;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  animation: slideIn-e4150081 0.3s ease-out;
}
.modal-title.data-v-e4150081 {
  font-size: 38rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
}
.modal-message.data-v-e4150081 {
  font-size: 30rpx;
  color: #555;
  line-height: 1.6;
  margin-bottom: 40rpx;
}
.modal-button.data-v-e4150081 {
  background: linear-gradient(45deg, #4a90ee, #62b0ff);
  color: #fff;
  border-radius: 40rpx;
  font-size: 22rpx;
  height: 70rpx;
  width: 80%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 15rpx rgba(74, 144, 238, 0.3);
  transition: all 0.2s ease-in-out;
  border: none;
}
.modal-button.data-v-e4150081:after {
  border: none;
}
.modal-button.data-v-e4150081:active {
  transform: translateY(2rpx);
  box-shadow: 0 3rpx 8rpx rgba(74, 144, 238, 0.5);
}

/* 动画效果 */
@keyframes fadeIn-e4150081 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.bottom.data-v-e4150081 {
  margin-left: 40px;
  margin-top: 10px;
  font-size: 10px;
  color: #555;
  font-weight: 10px;
  text-align: center;
}
@keyframes slideIn-e4150081 {
from {
    transform: translateY(-50px);
    opacity: 0;
}
to {
    transform: translateY(0);
    opacity: 1;
}
}