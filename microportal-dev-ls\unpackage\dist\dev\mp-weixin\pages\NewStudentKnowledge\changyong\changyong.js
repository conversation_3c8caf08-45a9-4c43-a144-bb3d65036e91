"use strict";
const common_vendor = require("../../../common/vendor.js");
if (!Math) {
  CustomModal();
}
const CustomModal = () => "../common/CustomModal.js";
const _sfc_main = {
  __name: "changyong",
  setup(__props) {
    const bannerList = common_vendor.ref([{
      src: "https://placehold.co/600x400/A7C7E7/ffffff?text=新生指南"
    }]);
    common_vendor.onShareAppMessage(() => {
      return {
        title: "河南农业大学新生指南中心",
        path: "/pages/index/index",
        imageUrl: bannerList.value[0].src
      };
    });
    const navigateToWebview = (externalUrl) => {
      const encodedUrl = encodeURIComponent(externalUrl);
      common_vendor.index.navigateTo({
        url: `/pages/NewStudentKnowledge/web-view/web-view?url=${encodedUrl}`
      });
    };
    const isModalShow = common_vendor.ref(false);
    const modalContent = common_vendor.ref(`河南农业大学IT工作室隶属于学校信息化办公室，主要负责校园信息化建设与维护。

招募方向：
- 前端开发(Web/小程序)
- 后端开发
- UI/UX设计
- 运维技术

有意向的同学请关注"河南农业大学信息化办公室"公众号了解详情！`);
    const openModal = () => {
      isModalShow.value = true;
    };
    const closeModal = () => {
      isModalShow.value = false;
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(closeModal),
        b: common_vendor.p({
          visible: isModalShow.value,
          title: "河南农业大学IT工作室",
          content: modalContent.value,
          confirmText: "好的",
          showCancel: false
        }),
        c: common_vendor.o(openModal),
        d: common_vendor.o(($event) => navigateToWebview("https://moments.henau.edu.cn/#/Index?code=Ikh9Uvt16qVCRZibgIznVqqcc4hljPAF&state=STATE"))
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5c9efd14"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/NewStudentKnowledge/changyong/changyong.js.map
