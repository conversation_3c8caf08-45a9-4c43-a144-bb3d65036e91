{"version": 3, "file": "landscape.js", "sources": ["pages/NewStudentKnowledge/landscape/landscape.vue", "../../test/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvTmV3U3R1ZGVudEtub3dsZWRnZS9sYW5kc2NhcGUvbGFuZHNjYXBlLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"title\">\n\t\t左右滑动,点击查看大图\n\t</view>\n\t<swiper\n\t\tclass=\"image-swiper\"\n\t\t:indicator-dots=\"false\"\n\t\t:autoplay=\"true\"\n\t\t:interval=\"3000\"\n\t\t:current=\"currentIndex\"\n\t\t:circular=\"true\"\n\t>\n\t\t<swiper-item\n\t\t\tv-for=\"(card, index) in originalCards\"\n\t\t\t:key=\"index\"\n\t\t\t@click=\"handleClick(index)\"\n\t\t>\n\t\t\t<view class=\"swiper-item-content\">\n\t\t\t\t<image class=\"card-image\" :src=\"card.image\" mode=\"aspectFit\"></image>\n\t\t\t\t<view class=\"card-description-box\">\n\t\t\t\t\t<text class=\"card-description\">{{ card.description }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</swiper-item>\n\t</swiper>\n\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue';\n\n// 原始图片数据\nconst originalCards = ref([\n\t{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6893031937e11_1754465049.jpg', description: '龙子湖' },\n\t{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/68930318e4fd0_1754465048.jpg', description: '龙子湖' },\n\t{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6893031859f14_1754465048.jpg', description: '龙子湖' },\n\t{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6893031709707_1754465047.jpg', description: '龙子湖' },\n\t{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/689302aa47ceb_1754464938.jpg', description: '文化路' },\n\t{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/689302aa43fd3_1754464938.jpg', description: '文化路' },\n\t{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6893026bcb788_1754464875.jpg', description: '桃李园' },\n\t{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6893026bb8318_1754464875.jpg', description: '桃李园' },\n\t{ image: 'https://itstudio.henau.edu.cn/image_hosting/uploads/6893026b97164_1754464875.jpg', description: '桃李园' },\n\t{ image: \"https://itstudio.henau.edu.cn/image_hosting/uploads/689302aa255d8_1754464938.jpg\", description: '文化路' },\n\t{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689302aa349a3_1754464938.jpg', description: '文化路' },\n\t{ image:\"https://itstudio.henau.edu.cn/image_hosting/uploads/6893026bb09d8_1754464875.jpg\", description: '桃李园' },\n\t{ image:\"https://itstudio.henau.edu.cn/image_hosting/uploads/6893026b8c8e3_1754464875.jpg\", description: '桃李园' },\n\t{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316ca6f20b_1754470090.png',description:'许昌校区'},\n\t{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316ca55caf_1754470090.png',description:'许昌校区'},\n\t{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316ca2ca47_1754470090.png',description:'许昌校区'},\n\t{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316c9f25af_1754470089.png',description:'许昌校区'},\n\t{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316ca0cc9a_1754470090.png',description:'许昌校区'},\n\t{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316ca217bb_1754470090.png',description:'许昌校区'},\n\t{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316c9e5ecc_1754470089.png',description:'许昌校区'},\n\t{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316c97bb8b_1754470089.png',description:'许昌校区'},\n\t{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316c93ac27_1754470089.png',description:'许昌校区'},\n\t{ image:'https://itstudio.henau.edu.cn/image_hosting/uploads/689316c94b785_1754470089.png',description:'许昌校区'},\n\t{ image:\"https://itstudio.henau.edu.cn/image_hosting/uploads/689316c8cc4a4_1754470088.png\" , description:\"许昌校区\"}\n]);\n\nconst currentIndex = ref(0);\n\n// Swiper 切换事件\nconst swiperChange = (e) => {\n\tcurrentIndex.value = e.detail.current;\n};\n\n// 处理卡片点击事件，跳转到详情页\nconst handleClick = (index) => {\n\tconst clickedCard = originalCards.value[index];\n\tuni.navigateTo({\n\t\turl: `/pages/NewStudentKnowledge/detail/detail?imageUrl=${encodeURIComponent(clickedCard.image)}`\n\t});\n};\n\nonMounted(() => {\n\t// 页面加载时可以执行一些初始化操作，但此处不再需要复杂的动画逻辑\n});\n</script>\n\n<style scoped>\n.title {\n\tposition: absolute;\n\ttop: 100rpx; /* Adjusted top position */\n\tleft: 50%;\n\ttransform: translateX(-50%);\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n\tcolor: #000000;\n\ttext-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);\n\tz-index: 150;\n}\n\n.image-counter {\n\tposition: absolute;\n\ttop: 80rpx; /* 调整位置，避免与标题重叠 */\n\tleft: 50%;\n\ttransform: translateX(-50%);\n\tfont-size: 24rpx;\n\tcolor: #ffffff;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n\tpadding: 8rpx 16rpx;\n\tborder-radius: 16rpx;\n\tz-index: 150;\n}\n\n.image-swiper {\n\twidth: 100vw;\n\theight: 95vh;\n\t/* 更新为渐变背景 */\n\tbackground: linear-gradient(135deg, #5dfd9d, #fbebf3, #fce1d1, #e68079);\n\tbackground-size: 400% 400%;\n\tanimation: gradientAnimation 4s ease infinite;\n}\n\n@keyframes gradientAnimation {\n\t0% {\n\t\tbackground-position: 0% 50%;\n\t}\n\t50% {\n\t\tbackground-position: 100% 50%;\n\t}\n\t100% {\n\t\tbackground-position: 0% 50%;\n\t}\n}\n\n.swiper-item-content {\n\twidth: 100%;\n\theight: 100%;\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tposition: relative;\n\tborder-radius: 20rpx; /* 给 swiper-item-content 添加圆角 */\n\toverflow: hidden; /* 裁剪超出圆角的部分 */\n}\n\n.card-image {\n\twidth: 100%;\n\theight: 100%;\n\tobject-fit: contain;\n}\n\n.card-description-box {\n\tborder-radius: 10px;\n\tposition: absolute;\n\tbottom: 20px;\n\tleft: 50%;\n\ttransform: translateX(-50%);\n\twidth: 80%;\n\tpadding: 20rpx;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n\tcolor: #fff;\n\tfont-size: 28rpx;\n\ttext-align: center;\n\tbox-sizing: border-box;\n\tz-index: 2;\n}\n\n.card-description {\n\tline-height: 2;\n}\n\n.bottom-text {\n\tposition: fixed; /* 固定在视口底部 */\n\tbottom: -10px; /* Adjusted bottom position */\n\tleft: 0;\n\twidth: 100%;\n\ttext-align: center;\n\tpadding: 10rpx 0;\n\tcolor: #000000;\n\tfont-size: 24rpx;\n\tz-index: 160; /* 确保在最上层 */\n}\n</style>\n", "import MiniProgramPage from 'D:/新生小程序/microportal-dev-ls/pages/NewStudentKnowledge/landscape/landscape.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "onMounted"], "mappings": ";;;;;AAgCA,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACzB,EAAE,OAAO,oFAAoF,aAAa,MAAO;AAAA,MACjH,EAAE,OAAO,oFAAoF,aAAa,MAAO;AAAA,MACjH,EAAE,OAAO,oFAAoF,aAAa,MAAO;AAAA,MACjH,EAAE,OAAO,oFAAoF,aAAa,MAAO;AAAA,MACjH,EAAE,OAAO,oFAAoF,aAAa,MAAO;AAAA,MACjH,EAAE,OAAO,oFAAoF,aAAa,MAAO;AAAA,MACjH,EAAE,OAAO,oFAAoF,aAAa,MAAO;AAAA,MACjH,EAAE,OAAO,oFAAoF,aAAa,MAAO;AAAA,MACjH,EAAE,OAAO,oFAAoF,aAAa,MAAO;AAAA,MACjH,EAAE,OAAO,oFAAoF,aAAa,MAAO;AAAA,MACjH,EAAE,OAAM,oFAAoF,aAAa,MAAO;AAAA,MAChH,EAAE,OAAM,oFAAoF,aAAa,MAAO;AAAA,MAChH,EAAE,OAAM,oFAAoF,aAAa,MAAO;AAAA,MAChH,EAAE,OAAM,oFAAmF,aAAY,OAAM;AAAA,MAC7G,EAAE,OAAM,oFAAmF,aAAY,OAAM;AAAA,MAC7G,EAAE,OAAM,oFAAmF,aAAY,OAAM;AAAA,MAC7G,EAAE,OAAM,oFAAmF,aAAY,OAAM;AAAA,MAC7G,EAAE,OAAM,oFAAmF,aAAY,OAAM;AAAA,MAC7G,EAAE,OAAM,oFAAmF,aAAY,OAAM;AAAA,MAC7G,EAAE,OAAM,oFAAmF,aAAY,OAAM;AAAA,MAC7G,EAAE,OAAM,oFAAmF,aAAY,OAAM;AAAA,MAC7G,EAAE,OAAM,oFAAmF,aAAY,OAAM;AAAA,MAC7G,EAAE,OAAM,oFAAmF,aAAY,OAAM;AAAA,MAC7G,EAAE,OAAM,oFAAqF,aAAY,OAAM;AAAA,IAChH,CAAC;AAED,UAAM,eAAeA,cAAAA,IAAI,CAAC;AAQ1B,UAAM,cAAc,CAAC,UAAU;AAC9B,YAAM,cAAc,cAAc,MAAM,KAAK;AAC7CC,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK,qDAAqD,mBAAmB,YAAY,KAAK,CAAC;AAAA,MACjG,CAAE;AAAA,IACF;AAEAC,kBAAAA,UAAU,MAAM;AAAA,IAEhB,CAAC;;;;;;;;;;;;;;;;;AC3ED,GAAG,WAAW,eAAe;"}