<template>
  <view class="page-container">
    <!-- IT工作室招新提示栏 -->
    <view class="studio-banner">
      <view class="banner-content">
        <image class="studio-logo" src="https://itstudio.henau.edu.cn/image_hosting/uploads/6896e1973513c_1754718615.png"></image>
        <text class="banner-text">河南农业大学IT工作室，面向全校招募技术人才！</text>
	<CustomModal
	  :visible="isModalShow" 
	  title="河南农业大学IT工作室" 
	  :content="modalContent" 
	  confirmText="好的" 
	  :showCancel="false" 
	  @confirm="closeModal"
	/>		
        <view class="join-button" @click="openModal">
          <text>了解详情</text>
        </view>
      </view>
    </view>
    
    <view class="header">
      <text class="header-title">常用软件与平台</text>
    </view>

    <view class="card-section">
      <view class="card">
        <view class="card-title-container">
          <text class="card-title">常用平台</text>
        </view>

        <view class="platform-item">
          <text class="item-title">河南农业大学专属学习交流平台</text>
          <view class="link-group">
            <view class="link-button" @click="navigateToWebview('https://moments.henau.edu.cn/#/Index?code=Ikh9Uvt16qVCRZibgIznVqqcc4hljPAF&state=STATE')">
              <text>农宝圈</text>
            </view>
          </view>
        </view>

        <view class="platform-item">
          <text class="item-title">学生卡充值、电费充值</text>
          <view>
            <text class="text">请搜索"河南农业大学信息化办公室"公众号点击"校园卡"选项</text>
          </view>
        </view>
      </view>
    </view>

    <view class="card-section">
      <view class="card">
        <view class="card-title-container">
          <text class="card-title">常用软件</text>
        </view>
        <!-- 喜鹊儿图标 -->
        <view class="list-item">
          <view class="item-header">
            <image class="item-icon" src="https://itstudio.henau.edu.cn/image_hosting/uploads/6894bbcfaa122_1754577871.png" mode="aspectFit"></image>
            <text class="item-title">喜鹊儿</text>
          </view>
          <text class="item-desc">查课表、查成绩、选课、申请调课等</text>
        </view>
        <!-- 学习通图标 -->
        <view class="list-item">
          <view class="item-header">
            <image class="item-icon" src="https://itstudio.henau.edu.cn/image_hosting/uploads/6894bc14ee537_1754577940.png" mode="aspectFit"></image>
            <text class="item-title">学习通</text>
          </view>
          <text class="item-desc">刷网课、提交作业</text>
        </view>
        <!-- 大学生MOOC图标 -->
        <view class="list-item">
          <view class="item-header">
            <image class="item-icon" src="https://itstudio.henau.edu.cn/image_hosting/uploads/6894bbd621ad7_1754577878.png" mode="aspectFit"></image>
            <text class="item-title">大学生MOOC</text>
          </view>
          <text class="item-desc">刷网课、提交作业</text>
        </view>
        <view class="list-item">
          <view class="item-header">
            <image class="item-icon" src="https://itstudio.henau.edu.cn/image_hosting/uploads/6894bc7a903c2_1754578042.png" mode="aspectFit"></image>
            <text class="item-title">WE Learn ,词达人</text>
          </view>
          <text class="item-desc">大英刷课用</text>
        </view>
        <!-- 胖乖生活图标 -->
        <view class="list-item">
          <view class="item-header">
            <image class="item-icon" src="https://itstudio.henau.edu.cn/image_hosting/uploads/6894bc51c2a4a_1754578001.png" mode="aspectFit"></image>
            <text class="item-title">胖乖生活(许昌，龙子湖使用)</text>
          </view>
          <text class="item-desc">学校澡堂洗澡要用</text>
        </view>
        <view class="list-item">
          <view class="item-header">
            <image class="item-icon" src="https://itstudio.henau.edu.cn/image_hosting/uploads/6894bbd142a34_1754577873.png" mode="aspectFit"></image>
            <text class="item-title">大白U帮(桃李园用)</text>
          </view>
          <text class="item-desc">学校澡堂洗澡要用</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
// 修正：移除showModal的导入，因为它是uni的内置方法
import {
  onShareAppMessage
} from '@dcloudio/uni-app';
import {
  ref
} from 'vue';

// 假设有一个bannerList用于分享图片
const bannerList = ref([{
  src: 'https://placehold.co/600x400/A7C7E7/ffffff?text=新生指南'
}]);

// 微信小程序分享配置
onShareAppMessage(() => {
  return {
    title: '河南农业大学新生指南中心',
    path: '/pages/index/index',
    imageUrl: bannerList.value[0].src,
  };
});

/**
 * 导航到web-view页面，用于加载外部链接
 * @param {string} externalUrl 外部链接URL
 */
const navigateToWebview = (externalUrl) => {
  const encodedUrl = encodeURIComponent(externalUrl);
  uni.navigateTo({
    url: `/pages/NewStudentKnowledge/web-view/web-view?url=${encodedUrl}`
  });
};

import CustomModal from '../common/CustomModal.vue'; 

const isModalShow = ref(false);
// 内容里直接写 \n ，配合组件里的 white-space: pre-wrap 即可换行
const modalContent = ref(`河南农业大学IT工作室隶属于河南农业大学信息化办公室，主要负责校园信息化建设与维护。
-----------主要职务内容-------------
前端开发(Web/小程序)
后端开发
UI/UX设计
运维技术
-----------------------------------
有意向的同学请关注
"河南农业大学信息化办公室"公众号
我们会在文章中发布招聘信息
期待你的加入！`);

// 打开模态框
const openModal = () => {
  isModalShow.value = true;
};

// 关闭模态框（确认按钮回调）
const closeModal = () => {
  isModalShow.value = false;
  // 这里可写确认后的逻辑，比如埋点、跳转等
};
</script>

<style lang="scss" scoped>
// IT工作室招新提示栏样式
.studio-banner {
  background: linear-gradient(135deg, #4a90e2 0%, #5cb85c 100%);
  border-radius: 24rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 30rpx;
  color: white;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.studio-logo {
  width: 60rpx;
  height: 60rpx;
  border-radius: 10rpx;
  background-color: white;
  padding: 5rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.banner-text {
  font-size: 28rpx;
  line-height: 1.5;
  flex: 1;
  padding-right: 20rpx;
}

.join-button {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  border-radius: 16rpx;
  padding: 12rpx 20rpx;
  font-size: 26rpx;
  transition: all 0.2s ease;
  flex-shrink: 0;
  
  &:active {
    background-color: rgba(255, 255, 255, 0.3);
    transform: scale(0.95);
  }
}

// 页面基础样式
.page-container {
  padding: 30rpx;
  background-color: #f0f4f7;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.header-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #2c3e50;
}

.card-section {
  margin-bottom: 40rpx;
}

.card {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
  
  &:active {
    transform: translateY(2rpx);
  }
}

.card-title-container {
  position: relative;
  margin-bottom: 30rpx;
  padding-left: 20rpx;
  display: flex;
  align-items: center;
}

.card-title-container::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 10rpx;
  height: 90%;
  background-color: #6699CC;
  border-radius: 5rpx;
}

.card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #34495e;
  margin-left: 10rpx;
}

.text {
  color: #55aa00;
}

.list-item {
  display: flex;
  flex-direction: column;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e9ecef;
  
  &:last-child {
    border-bottom: none;
  }
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 5rpx;
}

.item-icon {
  border-radius: 10px;
  width: 48rpx;
  height: 48rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.item-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

.item-desc {
  font-size: 28rpx;
  color: #888;
  line-height: 1.5;
  margin-top: 5rpx;
  padding-left: 63rpx;
}

.platform-item {
  display: flex;
  flex-direction: column;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e9ecef;
  
  &:last-child {
    border-bottom: none;
  }
}

.link-group {
  margin-top: 10rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.link-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e0f0ff;
  color: #4a77a8;
  font-size: 28rpx;
  padding: 16rpx 30rpx;
  border-radius: 16rpx;
  margin-top: 10rpx;
  transition: background-color 0.2s ease, transform 0.1s ease, box-shadow 0.2s ease;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(74, 119, 168, 0.2);
  
  &:active {
    background-color: #cce0ff;
    transform: scale(0.97) translateY(2rpx);
    box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.15);
  }
}

.note-text {
  font-size: 24rpx;
  color: #777;
  margin-top: 10rpx;
}
</style>
