"use strict";const e=require("../../../common/vendor.js");Math||n();const n=()=>"../common/CustomModal.js",t={__name:"changyong",setup(n){const t=e.ref([{src:"https://placehold.co/600x400/A7C7E7/ffffff?text=新生指南"}]);e.onShareAppMessage((()=>({title:"河南农业大学新生指南中心",path:"/pages/index/index",imageUrl:t.value[0].src})));const o=e.ref(!1),a=e.ref('河南农业大学IT工作室隶属于河南农业大学信息化办公室，主要负责校园信息化建设与维护。\n-----------主要职务内容-------------\n前端开发(Web/小程序)\n后端开发\nUI/UX设计\n运维技术\n-----------------------------------\n有意向的同学请关注\n"河南农业大学信息化办公室"公众号\n我们会在文章中发布招聘信息\n期待你的加入！'),s=()=>{o.value=!0},c=()=>{o.value=!1};return(n,t)=>({a:e.o(c),b:e.p({visible:o.value,title:"河南农业大学IT工作室",content:a.value,confirmText:"好的",showCancel:!1}),c:e.o(s),d:e.o((n=>(n=>{const t=encodeURIComponent(n);e.index.navigateTo({url:`/pages/NewStudentKnowledge/web-view/web-view?url=${t}`})})("https://moments.henau.edu.cn/#/Index?code=Ikh9Uvt16qVCRZibgIznVqqcc4hljPAF&state=STATE")))})}},o=e._export_sfc(t,[["__scopeId","data-v-834b9f80"]]);t.__runtimeHooks=2,wx.createPage(o);
