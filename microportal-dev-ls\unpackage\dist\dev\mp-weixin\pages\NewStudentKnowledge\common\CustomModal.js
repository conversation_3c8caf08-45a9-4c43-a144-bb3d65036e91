"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = {
  __name: "CustomModal",
  props: {
    visible: {
      // 是否显示模态框
      type: Boolean,
      default: false
    },
    title: {
      // 标题
      type: String,
      default: ""
    },
    content: {
      // 内容，支持 \n 换行
      type: String,
      default: ""
    },
    confirmText: {
      // 确认按钮文字
      type: String,
      default: "确认"
    },
    cancelText: {
      // 取消按钮文字
      type: String,
      default: "取消"
    },
    showCancel: {
      // 是否显示取消按钮
      type: Boolean,
      default: false
    },
    confirmBtnColor: {
      // 确认按钮颜色
      type: String,
      default: "#4CAF50"
      // 更柔和的绿色
    },
    cancelBtnColor: {
      // 取消按钮颜色（边框和文字）
      type: String,
      default: "#607D8B"
      // 沉稳的灰色
    },
    lineHeight: {
      // 内容行高，用于微调排版
      type: Number,
      default: 48
      // 调整为更舒适的行高，单位 rpx
    },
    showButton: {
      // 是否显示按钮（极端情况可隐藏，比如纯提示）
      type: <PERSON>olean,
      default: true
    }
  },
  emits: ["confirm", "cancel"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    const onConfirm = () => {
      emit("confirm");
    };
    const onCancel = () => {
      emit("cancel");
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.title
      }, __props.title ? {
        b: common_vendor.t(__props.title)
      } : {}, {
        c: common_vendor.t(__props.content),
        d: __props.lineHeight + "rpx",
        e: __props.showButton
      }, __props.showButton ? common_vendor.e({
        f: __props.showCancel
      }, __props.showCancel ? {
        g: common_vendor.t(__props.cancelText),
        h: __props.cancelBtnColor,
        i: __props.cancelBtnColor,
        j: common_vendor.o(onCancel)
      } : {}, {
        k: common_vendor.t(__props.confirmText),
        l: __props.confirmBtnColor,
        m: common_vendor.o(onConfirm)
      }) : {}, {
        n: __props.visible ? 1 : "",
        o: __props.visible ? 1 : ""
      });
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a3f33ad9"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/pages/NewStudentKnowledge/common/CustomModal.js.map
