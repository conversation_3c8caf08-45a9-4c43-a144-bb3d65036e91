
/* 遮罩层：全屏、半透明背景 */
.custom-modal-mask.data-v-a3f33ad9 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0); /* 初始透明 */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  opacity: 0; /* 初始隐藏 */
  visibility: hidden; /* 初始不可见 */
  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
}
.custom-modal-mask.is-visible.data-v-a3f33ad9 {
  background-color: rgba(0, 0, 0, 0.6); /* 显示时变深 */
  opacity: 1;
  visibility: visible;
}

/* 模态框主体：居中、白色背景、圆角、阴影 */
.custom-modal.data-v-a3f33ad9 {
  width: 80%;
  max-width: 600rpx; /* 限制最大宽度，适配大屏幕 */
  background-color: #fff;
  border-radius: 20rpx; /* 增大圆角 */
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.15); /* 更柔和的阴影 */
  overflow: hidden;
  transform: scale(0.8); /* 初始缩小 */
  opacity: 0; /* 初始隐藏 */
  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 0.3s ease-in-out;
}
.custom-modal.is-active.data-v-a3f33ad9 {
  transform: scale(1); /* 显示时放大 */
  opacity: 1;
}

/* 标题样式：居中、加粗、内边距 */
.modal-title.data-v-a3f33ad9 {
  font-size: 36rpx; /* 增大字体 */
  font-weight: bold;
  text-align: center;
  padding: 40rpx 30rpx; /* 增大内边距 */
  color: #333; /* 更深的字体颜色 */
  border-bottom: 1rpx solid #ebebeb; /* 更浅的分割线 */
}

/* 内容容器：内边距，让 text 排版更舒适 */
.modal-content.data-v-a3f33ad9 {
  padding: 40rpx 30rpx; /* 增大内边距 */
  font-size: 30rpx; /* 增大字体 */
  color: #555; /* 更深的字体颜色 */
  text-align: center; /* 内容居中 */
}

/* 关键：让 text 支持 \n 换行 */
.content-text.data-v-a3f33ad9 {
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 按钮容器：flex 布局，平分空间（有取消按钮时） */
.modal-btns.data-v-a3f33ad9 {
  display: flex;
  border-top: 1rpx solid #ebebeb; /* 更浅的分割线 */
}

/* 按钮通用样式：flex 占比、文字居中、内边距 */
.modal-btn.data-v-a3f33ad9 {
  flex: 1;
  text-align: center;
  padding: 30rpx 0; /* 增大内边距 */
  font-size: 32rpx;
  cursor: pointer; /* 添加手型光标 */
  transition: background-color 0.2s ease, color 0.2s ease, transform 0.1s ease;
  border-radius: 0 0 20rpx 20rpx; /* 底部圆角 */
}

/* 确认按钮样式 */
.confirm-btn.data-v-a3f33ad9 {
  color: #fff;
}

/* 取消按钮样式：仅边框和文字颜色，背景透明 */
.cancel-btn.data-v-a3f33ad9 {
  background-color: transparent;
  border-right: 1rpx solid #ebebeb; /* 将 border-left 改为 border-right，让确认按钮始终在右侧 */
  border-radius: 0 0 0 20rpx; /* 左下角圆角 */
}

/* 按钮点击效果 */
.modal-btn.data-v-a3f33ad9:active {
  transform: scale(0.98);
  opacity: 0.8;
}

/* 确保当只有一个按钮时，它占据全部宽度并正确圆角 */
.modal-btns .confirm-btn.data-v-a3f33ad9:only-child {
    border-radius: 0 0 20rpx 20rpx;
    border-right: none; /* 移除当只有一个按钮时的右边框 */
}

