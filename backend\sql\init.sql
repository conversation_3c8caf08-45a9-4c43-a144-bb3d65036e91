-- 创建数据库
CREATE DATABASE IF NOT EXISTS henau_miniprogram CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE henau_miniprogram;

-- 创建用户信息表
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  henau_openid VARCHAR(100) COMMENT '用户唯一标识（校外用户为NULL）',
  mp_openid VARCHAR(100) NOT NULL COMMENT '微信OpenID',
  user_name VARCHAR(50) COMMENT '用户姓名（校外用户可能为NULL）',
  user_number VARCHAR(20) COMMENT '学/工号（校外用户为NULL）',
  user_section VARCHAR(100) COMMENT '学院/部门名称',
  user_phone VARCHAR(20) COMMENT '联系方式（需要单独申请权限）',
  user_nickname VARCHAR(50) COMMENT '自定义昵称（需要单独申请权限）',
  user_avatar_url TEXT COMMENT '头像链接（需要单独申请权限）',
  user_status TINYINT NOT NULL DEFAULT 6 COMMENT '身份状态：0本科生,1研究生,2教职工,3后勤/外聘,4已毕业,5联合研究生院,6校外用户',
  henau_bind_url TEXT COMMENT '绑定身份链接（校外用户使用）',
  session_key VARCHAR(100) COMMENT '会话密钥',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
  INDEX idx_henau_openid (henau_openid),
  INDEX idx_mp_openid (mp_openid),
  INDEX idx_user_number (user_number),
  INDEX idx_user_status (user_status),
  UNIQUE KEY unique_henau_openid (henau_openid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户信息表';

-- 创建用户操作日志表
CREATE TABLE IF NOT EXISTS user_logs (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  henau_openid VARCHAR(100) COMMENT '用户唯一标识',
  action VARCHAR(50) NOT NULL COMMENT '操作类型',
  description TEXT COMMENT '操作描述',
  ip_address VARCHAR(45) NOT NULL COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  request_data JSON COMMENT '请求数据',
  response_data JSON COMMENT '响应数据',
  status VARCHAR(20) DEFAULT 'success' COMMENT '操作状态：success, error, warning',
  error_message TEXT COMMENT '错误信息',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  INDEX idx_henau_openid (henau_openid),
  INDEX idx_action (action),
  INDEX idx_ip_address (ip_address),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (henau_openid) REFERENCES users(henau_openid) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户操作日志表';

