<template>
  <!-- Overlay layer -->
  <view class="custom-modal-mask" :class="{ 'is-visible': visible }">
    <!-- Modal body -->
    <view class="custom-modal" :class="{ 'is-active': visible }">
      <!-- Title area -->
      <view class="modal-title" v-if="title">
        {{ title }}
      </view>
      <!-- Content area: use text with white-space for \n line breaks -->
      <view class="modal-content">
        <text class="content-text" :style="{ lineHeight: lineHeight + 'rpx' }">
          {{ content }}
        </text>
      </view>
      <!-- Buttons area -->
      <view class="modal-btns" v-if="showButton">
        <view
          class="modal-btn cancel-btn"
          :style="{ borderColor: cancelBtnColor, color: cancelBtnColor }"
          @click="onCancel"
          v-if="showCancel"
        >
          {{ cancelText }}
        </view>
        <view
          class="modal-btn confirm-btn"
          :style="{ backgroundColor: confirmBtnColor }"
          @click="onConfirm"
        >
          {{ confirmText }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

// Component props
const props = defineProps({
  visible: { type: Boolean, default: false }, // Whether to show the modal
  title: { type: String, default: '' },       // Title
  content: { type: String, default: '' },     // Content, supports \n for line breaks
  confirmText: { type: String, default: '确认' }, // Confirm button text
  cancelText: { type: String, default: '取消' },   // Cancel button text
  showCancel: { type: Boolean, default: false }, // Whether to show the cancel button
  confirmBtnColor: { type: String, default: '#4CAF50' }, // Confirm button color
  cancelBtnColor: { type: String, default: '#607D8B' },  // Cancel button color (border and text)
  lineHeight: { type: Number, default: 48 },     // Content line height in rpx
  showButton: { type: Boolean, default: true }   // Whether to show buttons (can be hidden for pure prompts)
});

// Component emits
const emit = defineEmits(['confirm', 'cancel']);

// Confirm button logic
const onConfirm = () => {
  emit('confirm');
};

// Cancel button logic
const onCancel = () => {
  emit('cancel');
};
</script>

<style scoped lang="scss">
.custom-modal-mask {
  position: fixed;
  inset: 0; /* Shorthand for top, right, bottom, left */
  background-color: rgba(0, 0, 0, 0);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;

  &.is-visible {
    background-color: rgba(0, 0, 0, 0.6);
    opacity: 1;
    visibility: visible;
  }
}

.custom-modal {
  width: 80%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.15);
  overflow: hidden;
  transform: scale(0.8);
  opacity: 0;
  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 0.3s ease-in-out;

  &.is-active {
    transform: scale(1);
    opacity: 1;
  }
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  padding: 40rpx 30rpx;
  color: #333;
  border-bottom: 1rpx solid #ebebeb;
}

.modal-content {
  padding: 40rpx 30rpx;
  font-size: 30rpx;
  color: #555;
  text-align: center;

  .content-text {
    white-space: pre-wrap;
    word-wrap: break-word;
  }
}

.modal-btns {
  display: flex;
  border-top: 1rpx solid #ebebeb;

  .modal-btn {
    flex: 1;
    text-align: center;
    padding: 30rpx 0;
    font-size: 32rpx;
    cursor: pointer;
    transition: background-color 0.2s ease, color 0.2s ease, transform 0.1s ease;
    border-radius: 0 0 20rpx 20rpx;

    &.confirm-btn {
		
      color: #ffffff;
      &:only-child {
        border-radius: 0 0 20rpx 20rpx;
        border-right: none;
      }
    }

    &.cancel-btn {
      background-color: transparent;
      border-right: 1rpx solid #ebebeb;
      border-radius: 0 0 0 20rpx; /* Bottom-left corner rounded */
    }

    &:active {
      transform: scale(0.98);
      opacity: 0.8;
    }
  }
}
</style>
