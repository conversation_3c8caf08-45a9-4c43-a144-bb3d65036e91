<template>
  <view class="container">
    <!-- 顶部状态栏占位，适配刘海屏 -->
    <view class="status-bar-placeholder"></view>

    <scroll-view class="main-scroll-view" scroll-y="true">
      <view class="content-wrapper">
        <!-- 欢迎标题 -->
        <view class="welcome-title-wrapper">
          <text class="welcome-title">欢迎来到河南农业大学许昌校区</text>
        </view>

        <!-- 新同学问候 -->
        <view class="greeting-section">
          <text class="greeting-text">你好，新同学：</text>
        </view>

        <!-- 一、校区基础信息 -->
        <view class="section-title-wrapper">
          <text class="section-title">一、校区基础信息</text>
        </view>

        <view class="info-paragraph">
          <text class="info-strong">1. 地理位置</text>
          <text class="info-text">
            <text class="newline"/>• 校区地址：河南省许昌市建安区河南农业大学
            <text class="newline"/>• 生活配套：
            <text class="newline"/>▪ 校内：食堂、超市、快递站（支持主流快递，网购前记得
            <text class="info-strong-blue">切换常用地址</text>）
            <text class="newline"/>▪ 校外：北海胖东来商圈、市中心（公交直达），校门口夜间设小吃摊。
          </text>
          <text class="info-strong">2.交通路线</text>
          <text class="info-text">
            <text class="newline"/>•家长来送：直接导航河南省许昌市建安区河南农业大学许昌校区
            <text class="newline"/>•坐火车（一般是在许昌站下车）：乘坐
            <text class="info-strong-blue">Y66路</text>公交车(运营时间：6:00～19:00)、打车或者其他
            <text class="newline"/>•坐高铁（一般是在许昌东站下车）：乘坐
            <text class="info-strong-blue">29路</text>公交车(运营时间：6:00～19:00)、打车或者其他
          </text>
        </view>

        <!-- 二、宿舍与生活 -->
        <view class="section-title-wrapper">
          <text class="section-title">二、宿舍与生活</text>
        </view>

        <view class="info-list">
          <view class="list-item">
            <text class="list-item-number">3.</text>
            <view class="list-item-content">
              <text class="info-strong">宿舍配置</text>
              <text class="info-text">
                <text class="newline"/>• 房型：4-6人间（随机分配），新楼配备
                <text class="info-strong-blue">独卫</text>（仅冷水），老楼无独立卫浴。
                <text class="newline"/>• 设施：
                <text class="newline"/>▪ 统一配置
                <text class="info-strong-blue">空调</text>（需付租金）、
                <text class="info-strong-blue">暖气</text>、
                <text class="info-strong-blue">上床下桌</text>。
                <text class="newline"/>▪ 床铺尺寸：0.9m×2.0m，允许军训后挂床帘。
                <text class="newline"/>• 水电：24小时不断电，电费超额需自行充值（微信公众号“河南农业大学信息化办公室”办理，充值前请明晰你在哪个寝室）。
              </text>
            </view>
          </view>
          <view class="list-item">
            <text class="list-item-number">4.</text>
            <view class="list-item-content">
              <text class="info-strong">洗浴与饮水</text>
              <text class="info-text">
                <text class="newline"/>• 澡堂为单间隔间（使用“胖乖生活”APP，为避免在澡堂被硬控半天，尽量在洗澡前下载好），新楼独卫无热水仅限冷水洗漱。
                <text class="newline"/>• 每层设饮水机，扫码支付使用，无需办理水卡。
              </text>
            </view>
          </view>
          <view class="list-item">
            <text class="list-item-number">5.</text>
            <view class="list-item-content">
              <text class="info-strong">违禁事项</text>
              <text class="info-text">
                <text class="newline"/>• 禁止使用吹风机、电煮锅等大功率电器（触发跳闸并通报）。
                <text class="newline"/>• 禁止私接电器，学校不定期抽查。
              </text>
            </view>
          </view>
        </view>

        <!-- 三、学习与校园管理 -->
        <view class="section-title-wrapper">
          <text class="section-title">三、学习与校园管理</text>
        </view>

        <view class="info-paragraph">
          <text class="info-strong">6. 课程与考试</text>
          <text class="info-text">
            <text class="newline"/>• 早晚自习：软件学院固定晚自习，其他学院依安排而定。
            <text class="newline"/>• 挂科风险：专业课需认真听讲+课后练习，突击备考可能挂科。
            <text class="newline"/>• 转专业：
            <text class="newline"/>▪ 大一下学期开放申请（需笔试/面试），无挂科、无违纪可申请。
            <text class="newline"/>▪ 原则：不可跨校区转专业（如许昌→郑州），参军退伍后可自由转专业。
          </text>
          <text class="info-strong">7. 网络与电子设备</text>
          <text class="info-text">
            <text class="newline"/>• 校园网：仅支持移动网络，建议办理校园卡提升网速（非强制，与选课无关）。
            <text class="newline"/>• 电脑要求：计算机类专业必需，其他专业建议配置（5-6K性价比机型足够）。
          </text>
          <text class="info-strong">8. 校园服务</text>
          <text class="info-text">
            <text class="newline"/>• 校医院：处理感冒、外伤等常见病，校外转诊可报销（具体流程入校后通知）。
            <text class="newline"/>• 快递站：校内集中收发点，开学季避免寄大件（易堆积）。
          </text>
        </view>

        <!-- 四、入学报到须知 -->
        <view class="section-title-wrapper">
          <text class="section-title">四、入学报到须知</text>
        </view>

        <view class="info-paragraph">
          <text class="info-strong">9. 必备材料</text>
          <text class="info-text">
            <text class="newline"/>• 录取通知书、身份证及复印件、学籍档案、一寸/二寸证件照（蓝白底各12张+4张）。
            <text class="newline"/>• 无需携带现金（学费/生活费存银行卡），校园卡开学统一发放（就餐/门禁/洗澡一卡通）。
          </text>
          <text class="info-strong">10. 报到流程</text>
          <text class="info-text">
            <text class="newline"/>• 时间：仅限报到当日（不可提前入住），建议外省学生提前预订周边酒店。
            <text class="newline"/>• 系统：8月底开放迎新系统（yxxt.henau.edu.cn），完成信息填报、入学答题、军训服购买。
            <text class="newline"/>• 联系：8月中下旬分配助班并建立班级群（通知学号、宿舍等信息）。
          </text>
          <text class="info-strong">11. 军训安排</text>
          <text class="info-text">
            <text class="newline"/>• 时长约2周（含周末），统一购买军训服（自备腰带防松垮）。
            <text class="newline"/>• 内容：军姿、跑操等基础训练，注意防晒并及时反馈身体不适。
          </text>
        </view>

        <!-- 五、校园资源与支持 -->
        <view class="section-title-wrapper">
          <text class="section-title">五、校园资源与支持</text>
        </view>

        <view class="info-paragraph">
          <text class="info-strong">12. 资助政策</text>
          <text class="info-text">
            <text class="newline"/>• 奖学金：国家奖学金（8000元/年）、国家励志奖学金（5000元/年）、校级奖学金（最高2000元/年）。
            <text class="newline"/>• 助学金：覆盖约25%在校生，平均3300元/年。
            <text class="newline"/>• 助学贷款：最高16000元/年（在校期间免息），需学号办理（8月中旬公布）。
            <text class="newline"/>• 绿色通道：特困生可先入学后补手续。
          </text>
          <text class="info-strong">13. 社团与活动</text>
          <text class="info-text">
            <text class="newline"/>• 社团举例：青年志愿者协会、IT工作室、羽毛球队、礼仪队、微爱公益协会等。
            <text class="newline"/>• 大型活动：军训汇演、迎新晚会、“百团大战”（社团招新）。
          </text>
        </view>

        <!-- 六、安全提醒 -->
        <view class="section-title-wrapper">
          <text class="section-title">六、安全提醒</text>
        </view>

        <view class="info-paragraph">
          <text class="info-text">
            14.
            <text class="info-strong-blue">警惕陌生链接</text>（尤其QQ邮箱），
            <text class="info-strong-blue">不泄露身份证</text>、
            <text class="info-strong-blue">学号</text>、
            <text class="info-strong-blue">银行卡信息</text>。
            <text class="newline"/>15.
            <text class="info-strong-blue">团关系转接</text>待分班后由助班通知（8月中下旬）。
            <text class="newline"/>16. 校园
            <text class="info-strong-blue">无早操</text>但需早起、无志愿时长强制要求，晚归可能随机抽查。
          </text>
          <text class="info-text small-text">
            温馨提示：更多动态请关注河南农业大学官网及招生办热线（0371-56990360/56990366）
            <text class="newline"/>该信息皆有在校学生回答，请注意甄别未经证实的信息。
          </text>
        </view>
      </view>
	  
	  
	 <!-- 返回顶部按钮和阅读进度 -->
	 <view class="scroll-controls">
	 	<text class="reading-progress">{{ Math.round(readingProgress) }}%</text>
	 	<button class="back-to-top-button" @click="scrollToTop">
	 		<!-- 使用图片作为返回顶部图标 -->
	 		<image class="top-arrow-icon" src="https://itstudio.henau.edu.cn/image_hosting/uploads/6893244343dd4_1754473539.png"></image>
	 	</button>
	 </view>
	  
	  
	  
    </scroll-view>
  </view>
  
  
</template>


<script setup>
	import { ref } from 'vue';
	import { onPageScroll, onReady } from '@dcloudio/uni-app';
	
	// 跟踪阅读进度
	const readingProgress = ref(0);
	// 页面总可滚动高度
	let totalScrollHeight = 0; 
	
	onReady(() => {
		// 页面渲染完成后，获取内容区域的总高度
		uni.createSelectorQuery().select('.main-scroll-view').boundingClientRect(rect => {
			if (rect) {
				// 获取 scroll-view 的实际内容高度
				// 注意：uni-app的scroll-view在某些情况下其boundingClientRect.height可能就是其可视高度
				// 更准确的做法是获取其内部内容的实际高度
				uni.createSelectorQuery().select('.content-wrapper').boundingClientRect(contentRect => {
					if (contentRect) {
						// 总可滚动高度 = 内容高度 - 视口高度（或scroll-view自身高度）
						// 这里使用uni.getSystemInfoSync().windowHeight作为视口高度的参考
						totalScrollHeight = contentRect.height - uni.getSystemInfoSync().windowHeight;
						if (totalScrollHeight < 0) totalScrollHeight = 0; // 防止负值
					}
				}).exec();
			}
		}).exec();
	});
	
	onPageScroll((e) => {
		if (totalScrollHeight > 0) {
			readingProgress.value = (e.scrollTop / totalScrollHeight) * 100;
			if (readingProgress.value > 100) readingProgress.value = 100; // 确保不超过100%
		} else {
			readingProgress.value = 0;
		}
	});
	
	// 滚动到顶部的方法
	const scrollToTop = () => {
		uni.pageScrollTo({
			scrollTop: 0,
			duration: 300
		});
	};
</script>


<style lang="scss" scoped>
	
	/* 返回顶部按钮和阅读进度样式 */
	.scroll-controls {
		position: fixed;
		bottom: 130rpx; /* 距离底部 */
		right: 20rpx; /* 距离右侧 */
		display: flex;
		flex-direction: column;
		align-items: center;
		z-index: 99; /* 确保在内容之上，但在弹窗之下 */
	}
	
	.reading-progress {
		background-color: rgba(0, 0, 0, 0.6);
		color: #fff;
		font-size: 24rpx;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		margin-bottom: 10rpx;
		white-space: nowrap; /* 防止百分比换行 */
	}
	
	.back-to-top-button {
		background-color: #0f4c81; /* 匹配主题色 */
		color: #fff;
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%; /* 圆形按钮 */
		display: flex; /* 使用flexbox来居中图片 */
		justify-content: center;
		align-items: center;
		box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
		border: none;
		padding: 0; /* 移除默认padding */
	} 
	
	.top-arrow-icon {
		width: 80rpx; /* 图片大小 */
		height: 80rpx;
		/* 可以添加滤镜来改变颜色，如果图片是黑色的 */
		/* filter: invert(100%); */
	}
	
.container {
  display: flex;
  flex-direction: column;
  height: 100vh; 
  background-color: #f0f2f5; /* 更柔和的页面背景色 */
}

/* 顶部状态栏占位，用于适配刘海屏等设备 */
.status-bar-placeholder {
  height: var(--status-bar-height); /* uni-app 提供的状态栏高度变量 */
  width: 100%;
  background-color: #ffffff; /* 状态栏背景色 */
}

.main-scroll-view {
  flex: 1; /* 占据剩余空间，允许内容滚动 */
  box-sizing: border-box; /* 边框盒模型 */
}

.content-wrapper {
  width: 100%;
  max-width: 750px; /* 限制内容最大宽度，模拟原 HTML 的 750px */
  margin: 0 auto; /* 居中显示 */
  background-color: #ffffff; /* 内容区域背景色 */
  padding: 20px; /* 增加内容区域内边距 */
  box-sizing: border-box; /* 边框盒模型 */
  font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Helvetica Neue", "Microsoft YaHei UI", "Microsoft YaHei", Arial, sans-serif;
  font-size: 16px;
  line-height: 1.8; /* 增加行高，提升可读性 */
  color: #333333; /* 默认文字颜色 */
  border-radius: 12px; /* 添加圆角 */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08); /* 柔和的阴影 */
}

/* 欢迎标题样式 */
.welcome-title-wrapper {
  text-align: center;
  margin: 1.5em auto 1.5em; /* 调整上下间距 */
  padding: 0 1em 10px; /* 增加底部内边距 */
  border-bottom: 3px solid #0F4C81; /* 稍微加粗底部边框 */
  display: flex;
  justify-content: center;
  align-items: center;
  width: fit-content;
  margin-top: 0;
}

.welcome-title {
  font-size: 22px; /* 稍微增大字体 */
  font-weight: bold;
  line-height: 1.5; /* 调整行高 */
  color: #2c3e50; /* 更深沉的颜色 */
  white-space: nowrap;
}

/* 新同学问候样式 */
.greeting-section {
  padding-left: 12px; /* 增加左侧内边距 */
  border-left: 4px solid #0F4C81; /* 稍微加粗左侧边框 */
  margin: 2.5em 0 1.2em 0; /* 增加上下间距 */
}

.greeting-text {
  font-size: 18px; /* 稍微增大字体 */
  line-height: 1.3;
  font-weight: bold;
  color: #34495e; /* 更深沉的颜色 */
}

/* 各章节标题样式 */
.section-title-wrapper {
  text-align: center;
  margin: 4em auto 2.5em; /* 增加上下间距 */
  padding: 8px 15px; /* 增加内边距，形成“药丸”形状 */
  background: linear-gradient(to right, #0F4C81, #2c7bb6); /* 渐变背景 */
  border-radius: 25px; /* 圆角，形成“药丸”形状 */
  display: flex;
  justify-content: center;
  align-items: center;
  width: fit-content;
  box-shadow: 0 2px 8px rgba(15, 76, 129, 0.3); /* 添加阴影 */
}

.section-title {
  font-size: 20px; /* 稍微增大字体 */
  font-weight: bold;
  line-height: 1.5;
  color: #fff;
  white-space: nowrap;
}

/* 普通信息段落样式 */
.info-paragraph {
  margin: 1.8em 0; /* 调整上下间距，左右由 content-wrapper 决定 */
  letter-spacing: 0.05em; /* 稍微调整字间距 */
  line-height: 1.8; /* 统一行高 */
}

/* 列表信息样式 */
.info-list {
  padding-left: 0; /* 移除 ul/ol 默认的左边距 */
  margin-left: 0;
  line-height: 1.8; /* 统一行高 */
}

.list-item {
  display: flex;
  margin-bottom: 0.8em; /* 增加列表项之间的间距 */
  align-items: flex-start;
}

.list-item-number {
  font-size: 16px;
  line-height: 1.8;
  margin-right: 0.6em; /* 调整数字和内容之间的间距 */
  flex-shrink: 0;
  font-weight: bold; /* 数字加粗 */
  color: #0F4C81; /* 数字颜色 */
}

.list-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 粗体文本样式 */
.info-strong {
  font-weight: bold;
  color: #0F4C81; /* 保持原蓝色 */
}

/* 普通文本样式 */
.info-text {
  font-size: 16px;
  color: #333333;
  line-height: 1.8;
  letter-spacing: 0.05em;
  display: block;
}

/* 蓝色粗体文本样式 */
.info-strong-blue {
  font-weight: bold;
  color: #0F4C81;
}

/* 自定义换行符样式，用于模拟 <br/> */
.newline {
  display: block;
  height: 0.5em; /* 控制换行间距 */
  content: '';
}

/* 底部小字提示 */
.small-text {
  font-size: 13px; /* 适当缩小字体 */
  color: #7f8c8d; /* 更柔和的灰色 */
  margin-top: 2em; /* 增加与上方内容的间距 */
  line-height: 1.6;
  display: block;
  text-align: justify; /* 两端对齐 */
}
</style>
