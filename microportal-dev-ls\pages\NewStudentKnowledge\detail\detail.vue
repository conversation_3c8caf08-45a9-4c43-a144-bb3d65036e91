<template>
	<view class="detail-page" @tap="closeDetailPage">
		<movable-area class="movable-area">
			<movable-view
				class="movable-view"
				direction="all"
				scale
				scale-min="1"
				scale-max="3"
				:scale-value="scaleValue"
				out-of-bounds
				@tap.stop="resetScaleAndClose"
			>
				<image :src="imageUrl" mode="widthFix" class="full-image" @load="onImageLoad"></image>
			</movable-view>
		</movable-area>
		<text class="close-tip">双指可缩放图片，单击任意处返回</text>
	</view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

const imageUrl = ref('');
const scaleValue = ref(1); // 用于控制图片缩放的初始值

// 在页面加载时获取传递过来的参数
onLoad((options) => {
	if (options.imageUrl) {
		imageUrl.value = decodeURIComponent(options.imageUrl);
	}
});

// 图片加载完成时重置缩放值，确保每次进入页面都是初始大小
const onImageLoad = () => {
	scaleValue.value = 1;
};

// 点击 movable-view 或背景时关闭页面
const closeDetailPage = () => {
	uni.navigateBack();
};

// 点击图片时重置缩放并关闭页面
const resetScaleAndClose = () => {
	scaleValue.value = 1; // 确保在关闭前重置缩放，避免下次进入时仍是放大状态
	uni.navigateBack();
};
</script>

<style scoped>
.detail-page {
	display: flex;
	flex-direction: column; /* 调整为列布局，以便提示文字在底部 */
	justify-content: center;
	align-items: center;
	width: 100vw;
	height: 100vh;
	background-color: #000; /* 黑色背景，更好地展示图片 */
	position: fixed; /* 固定定位，确保覆盖整个屏幕 */
	top: 0;
	left: 0;
	z-index: 9999; /* 确保在最上层 */
	overflow: hidden; /* 防止页面滚动 */
}

.movable-area {
	width: 100%;
	height: 100%;
	/* 确保 movable-area 能够覆盖整个可见区域，以便拖动和缩放 */
}

.movable-view {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	/* movable-view 的大小通常设置为 100% 100% */
}

.full-image {
	width: 100%; /* 图片宽度填充 movable-view */
	height: auto; /* 高度自适应，保持图片比例 */
	/* mode="widthFix" 确保图片宽度固定，高度自适应 */
}

.close-tip {
	position: absolute;
	bottom: 40rpx;
	left: 50%;
	transform: translateX(-50%);
	color: rgba(255, 255, 255, 0.8);
	font-size: 24rpx;
	padding: 10rpx 30rpx;
	border-radius: 30rpx;
	background: rgba(0, 0, 0, 0.3);
	white-space: nowrap;
	z-index: 10000; /* 确保提示文字在最上层 */
}
</style>
