{"version": 3, "file": "changyong.js", "sources": ["pages/NewStudentKnowledge/changyong/changyong.vue", "../../test/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvTmV3U3R1ZGVudEtub3dsZWRnZS9jaGFuZ3lvbmcvY2hhbmd5b25nLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"page-container\">\n    <!-- IT工作室招新提示栏 -->\n    <view class=\"studio-banner\">\n      <view class=\"banner-content\">\n        <image class=\"studio-logo\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6896e1973513c_1754718615.png\"></image>\n        <text class=\"banner-text\">河南农业大学IT工作室隶属于信息化办公室，面向全校招募技术人才！</text>\r\n\t<CustomModal\r\n\t  :visible=\"isModalShow\" \r\n\t  title=\"河南农业大学IT工作室\" \r\n\t  :content=\"modalContent\" \r\n\t  confirmText=\"好的\" \r\n\t  :showCancel=\"false\" \r\n\t  @confirm=\"closeModal\"\r\n\t/>\t\t\n        <view class=\"join-button\" @click=\"openModal\">\n          <text>了解详情</text>\n        </view>\n      </view>\n    </view>\n    \n    <view class=\"header\">\n      <text class=\"header-title\">常用软件与平台</text>\n    </view>\n\n    <view class=\"card-section\">\n      <view class=\"card\">\n        <view class=\"card-title-container\">\n          <text class=\"card-title\">常用平台</text>\n        </view>\n\n        <view class=\"platform-item\">\n          <text class=\"item-title\">河南农业大学专属学习交流平台</text>\n          <view class=\"link-group\">\n            <view class=\"link-button\" @click=\"navigateToWebview('https://moments.henau.edu.cn/#/Index?code=Ikh9Uvt16qVCRZibgIznVqqcc4hljPAF&state=STATE')\">\n              <text>农宝圈</text>\n            </view>\n          </view>\n        </view>\n\n        <view class=\"platform-item\">\n          <text class=\"item-title\">学生卡充值、电费充值</text>\n          <view>\n            <text class=\"text\">请搜索\"河南农业大学信息化办公室\"公众号点击\"校园卡\"选项</text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <view class=\"card-section\">\n      <view class=\"card\">\n        <view class=\"card-title-container\">\n          <text class=\"card-title\">常用软件</text>\n        </view>\n        <!-- 喜鹊儿图标 -->\n        <view class=\"list-item\">\n          <view class=\"item-header\">\n            <image class=\"item-icon\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6894bbcfaa122_1754577871.png\" mode=\"aspectFit\"></image>\n            <text class=\"item-title\">喜鹊儿</text>\n          </view>\n          <text class=\"item-desc\">查课表、查成绩、选课、申请调课等</text>\n        </view>\n        <!-- 学习通图标 -->\n        <view class=\"list-item\">\n          <view class=\"item-header\">\n            <image class=\"item-icon\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6894bc14ee537_1754577940.png\" mode=\"aspectFit\"></image>\n            <text class=\"item-title\">学习通</text>\n          </view>\n          <text class=\"item-desc\">刷网课、提交作业</text>\n        </view>\n        <!-- 大学生MOOC图标 -->\n        <view class=\"list-item\">\n          <view class=\"item-header\">\n            <image class=\"item-icon\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6894bbd621ad7_1754577878.png\" mode=\"aspectFit\"></image>\n            <text class=\"item-title\">大学生MOOC</text>\n          </view>\n          <text class=\"item-desc\">刷网课、提交作业</text>\n        </view>\n        <view class=\"list-item\">\n          <view class=\"item-header\">\n            <image class=\"item-icon\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6894bc7a903c2_1754578042.png\" mode=\"aspectFit\"></image>\n            <text class=\"item-title\">WE Learn ,词达人</text>\n          </view>\n          <text class=\"item-desc\">大英刷课用</text>\n        </view>\n        <!-- 胖乖生活图标 -->\n        <view class=\"list-item\">\n          <view class=\"item-header\">\n            <image class=\"item-icon\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6894bc51c2a4a_1754578001.png\" mode=\"aspectFit\"></image>\n            <text class=\"item-title\">胖乖生活(许昌，龙子湖使用)</text>\n          </view>\n          <text class=\"item-desc\">学校澡堂洗澡要用</text>\n        </view>\n        <view class=\"list-item\">\n          <view class=\"item-header\">\n            <image class=\"item-icon\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6894bbd142a34_1754577873.png\" mode=\"aspectFit\"></image>\n            <text class=\"item-title\">大白U帮(桃李园用)</text>\n          </view>\n          <text class=\"item-desc\">学校澡堂洗澡要用</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\n// 修正：移除showModal的导入，因为它是uni的内置方法\nimport {\n  onShareAppMessage\n} from '@dcloudio/uni-app';\nimport {\n  ref\n} from 'vue';\n\n// 假设有一个bannerList用于分享图片\nconst bannerList = ref([{\n  src: 'https://placehold.co/600x400/A7C7E7/ffffff?text=新生指南'\n}]);\n\n// 微信小程序分享配置\nonShareAppMessage(() => {\n  return {\n    title: '河南农业大学新生指南中心',\n    path: '/pages/index/index',\n    imageUrl: bannerList.value[0].src,\n  };\n});\n\n/**\n * 导航到web-view页面，用于加载外部链接\n * @param {string} externalUrl 外部链接URL\n */\nconst navigateToWebview = (externalUrl) => {\n  const encodedUrl = encodeURIComponent(externalUrl);\n  uni.navigateTo({\n    url: `/pages/NewStudentKnowledge/web-view/web-view?url=${encodedUrl}`\n  });\n};\n\nimport CustomModal from '../common/CustomModal.vue'; \n\nconst isModalShow = ref(false);\n// 内容里直接写 \\n ，配合组件里的 white-space: pre-wrap 即可换行\nconst modalContent = ref(`河南农业大学IT工作室隶属于学校信息化办公室，主要负责校园信息化建设与维护。\n\n招募方向：\n- 前端开发(Web/小程序)\n- 后端开发\n- UI/UX设计\n- 运维技术\n\n有意向的同学请关注\"河南农业大学信息化办公室\"公众号了解详情！`);\n\n// 打开模态框\nconst openModal = () => {\n  isModalShow.value = true;\n};\n\n// 关闭模态框（确认按钮回调）\nconst closeModal = () => {\n  isModalShow.value = false;\n  // 这里可写确认后的逻辑，比如埋点、跳转等\n};\n</script>\n\n<style lang=\"scss\" scoped>\n// IT工作室招新提示栏样式\n.studio-banner {\n  background: linear-gradient(135deg, #4a90e2 0%, #5cb85c 100%);\n  border-radius: 24rpx;\n  padding: 20rpx 30rpx;\n  margin-bottom: 30rpx;\n  color: white;\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n}\n\n.banner-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.studio-logo {\n  width: 60rpx;\n  height: 60rpx;\n  border-radius: 10rpx;\n  background-color: white;\n  padding: 5rpx;\n  margin-right: 20rpx;\n  flex-shrink: 0;\n}\n\n.banner-text {\n  font-size: 28rpx;\n  line-height: 1.5;\n  flex: 1;\n  padding-right: 20rpx;\n}\n\n.join-button {\n  background-color: rgba(255, 255, 255, 0.2);\n  border: 1rpx solid rgba(255, 255, 255, 0.4);\n  border-radius: 16rpx;\n  padding: 12rpx 20rpx;\n  font-size: 26rpx;\n  transition: all 0.2s ease;\n  flex-shrink: 0;\n  \n  &:active {\n    background-color: rgba(255, 255, 255, 0.3);\n    transform: scale(0.95);\n  }\n}\n\n// 页面基础样式\n.page-container {\n  padding: 30rpx;\n  background-color: #f0f4f7;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 40rpx;\n}\n\n.header-title {\n  font-size: 40rpx;\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.card-section {\n  margin-bottom: 40rpx;\n}\n\n.card {\n  background-color: #fff;\n  border-radius: 24rpx;\n  padding: 30rpx;\n  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.08);\n  transition: transform 0.3s ease;\n  \n  &:active {\n    transform: translateY(2rpx);\n  }\n}\n\n.card-title-container {\n  position: relative;\n  margin-bottom: 30rpx;\n  padding-left: 20rpx;\n  display: flex;\n  align-items: center;\n}\n\n.card-title-container::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 10rpx;\n  height: 90%;\n  background-color: #6699CC;\n  border-radius: 5rpx;\n}\n\n.card-title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #34495e;\n  margin-left: 10rpx;\n}\n\n.text {\n  color: #55aa00;\n}\n\n.list-item {\n  display: flex;\n  flex-direction: column;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #e9ecef;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.item-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 5rpx;\n}\n\n.item-icon {\n  border-radius: 10px;\n  width: 48rpx;\n  height: 48rpx;\n  margin-right: 15rpx;\n  flex-shrink: 0;\n}\n\n.item-title {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333;\n  line-height: 1.5;\n  flex: 1;\n}\n\n.item-desc {\n  font-size: 28rpx;\n  color: #888;\n  line-height: 1.5;\n  margin-top: 5rpx;\n  padding-left: 63rpx;\n}\n\n.platform-item {\n  display: flex;\n  flex-direction: column;\n  padding: 20rpx 0;\n  border-bottom: 1rpx solid #e9ecef;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n}\n\n.link-group {\n  margin-top: 10rpx;\n  display: flex;\n  flex-wrap: wrap;\n  gap: 20rpx;\n}\n\n.link-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #e0f0ff;\n  color: #4a77a8;\n  font-size: 28rpx;\n  padding: 16rpx 30rpx;\n  border-radius: 16rpx;\n  margin-top: 10rpx;\n  transition: background-color 0.2s ease, transform 0.1s ease, box-shadow 0.2s ease;\n  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1);\n  border: 1rpx solid rgba(74, 119, 168, 0.2);\n  \n  &:active {\n    background-color: #cce0ff;\n    transform: scale(0.97) translateY(2rpx);\n    box-shadow: 0 2rpx 5rpx rgba(0, 0, 0, 0.15);\n  }\n}\n\n.note-text {\n  font-size: 24rpx;\n  color: #777;\n  margin-top: 10rpx;\n}\n</style>\n", "import MiniProgramPage from 'D:/新生小程序/microportal-dev-ls/pages/NewStudentKnowledge/changyong/changyong.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onShareAppMessage", "uni"], "mappings": ";;;;;AA2IA,MAAA,cAAA,MAAA;;;;AAxBA,UAAA,aAAAA,cAAA,IAAA,CAAA;AAAA,MACA,KAAA;AAAA,IACA,CAAA,CAAA;AAGAC,kBAAAA,kBAAA,MAAA;AACA,aAAA;AAAA,QACA,OAAA;AAAA,QACA,MAAA;AAAA,QACA,UAAA,WAAA,MAAA,CAAA,EAAA;AAAA,MACA;AAAA,IACA,CAAA;AAMA,UAAA,oBAAA,CAAA,gBAAA;AACA,YAAA,aAAA,mBAAA,WAAA;AACAC,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,oDAAA,UAAA;AAAA,MACA,CAAA;AAAA,IACA;AAIA,UAAA,cAAAF,cAAAA,IAAA,KAAA;AAEA,UAAA,eAAAA,cAAA,IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCAQA;AAGA,UAAA,YAAA,MAAA;AACA,kBAAA,QAAA;AAAA,IACA;AAGA,UAAA,aAAA,MAAA;AACA,kBAAA,QAAA;AAAA,IAEA;;;;;;;;;;;;;;;;;;;ACjKA,GAAG,WAAW,eAAe;"}