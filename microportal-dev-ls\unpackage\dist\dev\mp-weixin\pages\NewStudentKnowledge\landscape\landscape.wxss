
.title.data-v-f0b1c6a3 {
	position: absolute;
	top: 100rpx; /* Adjusted top position */
	left: 50%;
	transform: translateX(-50%);
	font-size: 30rpx;
	font-weight: bold;
	color: #000000;
	text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
	z-index: 150;
}
.image-counter.data-v-f0b1c6a3 {
	position: absolute;
	top: 80rpx; /* 调整位置，避免与标题重叠 */
	left: 50%;
	transform: translateX(-50%);
	font-size: 24rpx;
	color: #ffffff;
	background-color: rgba(0, 0, 0, 0.5);
	padding: 8rpx 16rpx;
	border-radius: 16rpx;
	z-index: 150;
}
.image-swiper.data-v-f0b1c6a3 {
	width: 100vw;
	height: 95vh;
	/* 更新为渐变背景 */
	background: linear-gradient(135deg, #5dfd9d, #fbebf3, #fce1d1, #e68079);
	background-size: 400% 400%;
	animation: gradientAnimation-f0b1c6a3 4s ease infinite;
}
@keyframes gradientAnimation-f0b1c6a3 {
0% {
		background-position: 0% 50%;
}
50% {
		background-position: 100% 50%;
}
100% {
		background-position: 0% 50%;
}
}
.swiper-item-content.data-v-f0b1c6a3 {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	position: relative;
	border-radius: 20rpx; /* 给 swiper-item-content 添加圆角 */
	overflow: hidden; /* 裁剪超出圆角的部分 */
}
.card-image.data-v-f0b1c6a3 {
	width: 100%;
	height: 100%;
	object-fit: contain;
}
.card-description-box.data-v-f0b1c6a3 {
	border-radius: 10px;
	position: absolute;
	bottom: 20px;
	left: 50%;
	transform: translateX(-50%);
	width: 80%;
	padding: 20rpx;
	background-color: rgba(0, 0, 0, 0.5);
	color: #fff;
	font-size: 28rpx;
	text-align: center;
	box-sizing: border-box;
	z-index: 2;
}
.card-description.data-v-f0b1c6a3 {
	line-height: 2;
}
.bottom-text.data-v-f0b1c6a3 {
	position: fixed; /* 固定在视口底部 */
	bottom: -10px; /* Adjusted bottom position */
	left: 0;
	width: 100%;
	text-align: center;
	padding: 10rpx 0;
	color: #000000;
	font-size: 24rpx;
	z-index: 160; /* 确保在最上层 */
}
