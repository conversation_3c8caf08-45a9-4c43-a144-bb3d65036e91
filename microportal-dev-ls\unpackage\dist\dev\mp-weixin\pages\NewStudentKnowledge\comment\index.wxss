
/* 页面容器 */
.page-container.data-v-cc3a1b72 {
	width: 100%;
	min-height: 100vh; /* 确保页面高度至少为视口高度 */
	box-sizing: border-box;
}
.bottom.data-v-cc3a1b72{
	margin-left: 90px;
	font-size: 15px;
	font-weight: 1000;
	text-align: center;
	color: #7a776f;
	margin-top: 50rpx; /* 确保底部有足够空间 */
	padding-bottom: 20rpx; /* 底部填充 */
}

/* 内容区域包裹器 */
.content-wrapper.data-v-cc3a1b72 {
	width: 90%;
	margin: 0 auto;
	
	border-radius: 16rpx; /* 圆角 */
	/* 调整内容区域的内边距，左右各增加到30rpx */
	padding: 100rpx 30rpx 30rpx 30rpx; /* 上、右、下、左 */
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05); /* 轻微阴影 */
}

/* 贡献者按钮已移除样式 */
/* .contributor-button { ... } */


/* 标题样式 */
.header-section.data-v-cc3a1b72, .contributor-section .section-title.data-v-cc3a1b72 {
	text-align: center;
	margin: 0rpx auto 30rpx; /* 上下边距，左右居中 */
	border-bottom: 2rpx solid #338174; /* 底部边框 */
	padding-bottom: 10rpx; /* 边框与文字间距 */
	display: table; /* 使边框只包裹内容宽度 */
}
.image.data-v-cc3a1b72{
	width: 100%;
}
.section-title.data-v-cc3a1b72 {
	font-size: 33.6rpx;
	font-weight: bold;
	color: #000000; /* 标题颜色改为深蓝色，与主题色保持一致 */
	line-height: 1.75;
	white-space: nowrap; /* 防止标题换行 */
}

/* 问答项样式 */
.qa-item.data-v-cc3a1b72 {
	margin: 30rpx 0;
	line-height: 1.75;
	font-size: 28rpx;
	letter-spacing: 0.05em;
	color: #333333;
	text-align: justify;
}
.question.data-v-cc3a1b72 {
	font-weight: bold;
	color: #55aa00; /* 绿色问题 */
	display: block;
	margin-bottom: 10rpx;
}
.answer.data-v-cc3a1b72 {
	display: block;
}
.highlight.data-v-cc3a1b72 {
	color: #0f4c81; /* 高亮颜色改为深蓝色，与主题色保持一致 */
	font-weight: bold;
}


/* 贡献者名单直接展示的样式 */
.contributor-section.data-v-cc3a1b72 {
	margin-top: 80rpx; /* 与上方内容保持距离 */
	padding-top: 40rpx;
	border-top: 2rpx solid #e0e0e0; /* 顶部加一条分割线 */
}
.modal-contributors-list.data-v-cc3a1b72 { /* 复用原有的列表样式，但现在是直接在页面中 */
	display: flex;
	flex-direction: column;
	align-items: center;
	width: 100%;
}


/* 贡献者类别容器 */
.contributor-category.data-v-cc3a1b72 {
	width: 100%;
	margin-bottom: 40rpx; /* 类别之间的间距 */
}

/* 类别标题 */
.category-title.data-v-cc3a1b72 {
	font-size: 32rpx; /* 类别标题字号 */
	font-weight: bold;
	color: #609c63; /* 类别标题颜色 */
	margin-bottom: 20rpx; /* 标题与名字的间距 */
	display: block; /* 确保标题独占一行 */
	text-align: center;
	border-bottom: 2rpx solid #e0e0e0; /* 底部细线 */
	padding-bottom: 10rpx;
}

/* 名字网格布局 */
.names-grid.data-v-cc3a1b72 {
	display: flex;
	flex-wrap: wrap; /* 允许换行 */
	justify-content: center; /* 名字居中对齐 */
	gap: 20rpx 30rpx; /* 行间距和列间距 */
}
.contributor-name.data-v-cc3a1b72 {
	font-size: 30rpx; /* 贡献者名字字号稍大 */
	color: #4a4a4a; /* 更深的灰色 */
	line-height: 1.6;
	padding: 5rpx 0; /* 增加垂直内边距 */
	transition: color 0.2s ease;
}


/* 返回顶部按钮和阅读进度样式 */
.scroll-controls.data-v-cc3a1b72 {
	position: fixed;
	bottom: 130rpx; /* 距离底部 */
	right: 20rpx; /* 距离右侧 */
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 99; /* 确保在内容之上 */
}
.reading-progress.data-v-cc3a1b72 {
	background-color: rgba(0, 0, 0, 0.6);
	color: #fff;
	font-size: 24rpx;
	padding: 10rpx 20rpx;
	border-radius: 30rpx;
	margin-bottom: 10rpx;
	white-space: nowrap; /* 防止百分比换行 */
}
.back-to-top-button.data-v-cc3a1b72 {
	background-color: #0f4c81; /* 匹配主题色 */
	color: #fff;
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%; /* 圆形按钮 */
	display: flex; /* 使用flexbox来居中图片 */
	justify-content: center;
	align-items: center;
	box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);
	border: none;
	padding: 0; /* 移除默认padding */
}
.top-arrow-icon.data-v-cc3a1b72 {
	width: 80rpx; /* 图片大小 */
	height: 80rpx;
	/* 可以添加滤镜来改变颜色，如果图片是黑色的 */
	/* filter: invert(100%); */
}

/* 动画效果已移除 (弹窗) */
/* @keyframes fadeIn { ... } */
/* @keyframes slideIn { ... } */
