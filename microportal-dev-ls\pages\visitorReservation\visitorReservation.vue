<template>
	<!-- 访客预约系统 -->
	<web-view src="https://bwcfr.henau.edu.cn/visitor/#/pages/index/index">
		<cover-view class="close-view" @click="closeView()">
			<cover-image class="close-icon" src="/static/icon/public/home.png"></cover-image>
		</cover-view>
	</web-view>
</template>

<script setup>
	import {
		onShareAppMessage,
		onShareTimeline
	} from '@dcloudio/uni-app'
	const closeView = () => {
		uni.reLaunch({
			url: '/pages/index/index'
		})
	}
	onShareAppMessage((options) => {
		// 获取 webViewUrl
		const webViewUrl = options.webViewUrl;
		// 构建携带参数的路径
		const sharePath = `/pages/index/index?webViewUrl=${encodeURIComponent(webViewUrl)}`;
		return {
			path: sharePath,
		}
	})
	onShareTimeline(() => {})
</script>

<style>
	.close-view {
		background-color: #616161;
		border-radius: 50%;
		position: fixed;
		z-index: 99999;
		bottom: 19vh;
		right: 30px;
		visibility: visible !important;
		padding: 5px;
	}

	.close-icon {
		width: 30px;
		height: 30px;
	}
</style>