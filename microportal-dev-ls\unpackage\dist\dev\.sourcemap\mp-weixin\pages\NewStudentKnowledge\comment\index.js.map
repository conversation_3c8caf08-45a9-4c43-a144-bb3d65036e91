{"version": 3, "file": "index.js", "sources": ["pages/NewStudentKnowledge/comment/index.vue", "../../test/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvTmV3U3R1ZGVudEtub3dsZWRnZS9jb21tZW50L2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"page-container\">\n\t\t<!-- 贡献者按钮已移除，内容直接显示在底部 -->\n\n\t\t<view class=\"content-wrapper\">\n\t\t\t<view class=\"header-section\">\n\t\t\t\t<text class=\"section-title\">问答专栏</text>\n\t\t\t</view>\n\t\t\t<view class=\"qa-item\" v-for=\"(item, index) in registrationQa\" :key=\"'reg-' + index\">\n\t\t\t\t<text class=\"question\">{{ item.question }}</text>\n\t\t\t\t<text class=\"answer\">\n\t\t\t\t\t<text v-for=\"(part, pIndex) in parseAnswer(item.answer)\" :key=\"pIndex\" :class=\"{ 'highlight': part.type === 'highlight' }\">{{ part.content }}</text>\n\t\t\t\t</text>\n\t\t\t\t\n\t\t\t\t<view v-if=\"item.image_url != null\" @tap=previewImage(item.image_url)>\n\t\t\t\t\t<img class=\"image\" :src=item.image_url  alt=\"校历图片\" />\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 贡献者名单直接展示在这里 -->\n\t\t\t<view class=\"contributor-section\">\n\t\t\t\t<text class=\"section-title\">贡献者名单(不分先后)</text>\n\t\t\t\t<view class=\"modal-contributors-list\">\n\t\t\t\t\t<!-- 遍历贡献者类别 -->\n\t\t\t\t\t<view v-for=\"(category, catIndex) in contributors\" :key=\"'cat-' + catIndex\" class=\"contributor-category\">\n\t\t\t\t\t\t<text class=\"category-title\">{{ category.category }}</text>\n\t\t\t\t\t\t<view class=\"names-grid\">\n\t\t\t\t\t\t\t<text class=\"contributor-name\" v-for=\"(name, nameIndex) in category.names\" :key=\"'name-' + nameIndex\">{{ name }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"scroll-controls\">\n\t\t\t<text class=\"reading-progress\">{{ Math.round(readingProgress) }}%</text>\n\t\t\t<button class=\"back-to-top-button\" @click=\"scrollToTop\">\n\t\t\t\t<image class=\"top-arrow-icon\" src=\"https://itstudio.henau.edu.cn/image_hosting/uploads/6893244343dd4_1754473539.png\"></image>\n\t\t\t</button>\n\t\t</view>\n\t\t\n\t<!-- 触发按钮 -->\r\n\t<text class=\"open-btn bottom\" @click=\"openModal\">河南农业大学IT工作室(欢迎加入)</text>\r\n\t<!-- 引入自定义模态框组件 -->\r\n\t<CustomModal \r\n\t  :visible=\"isModalShow\" \r\n\t  title=\"河南农业大学IT工作室\" \r\n\t  :content=\"modalContent\" \r\n\t  confirmText=\"好的\" \r\n\t  :showCancel=\"false\" \r\n\t  @confirm=\"closeModal\"\r\n\t/>\t\n\t</view>\n</template>\n\n<script setup>\nimport { ref } from 'vue';\nimport { onPageScroll, onReady } from '@dcloudio/uni-app';\nimport qa from './Q&A.json'\n\n// 贡献者名单弹窗逻辑已移除\n// 动态贡献者名单，现在分为类别\nconst contributors = ref([\n\t{ category: '河南农业大学IT工作室成员', names: ['24级CLOWN','24级千木','24级非酋sama','24级XX','24级斯特','24级阿布学长','24级槿熙','24级昔文', '24级Pinging'] },\n\t{ category: '其他贡献者', names: ['22级卢荟胶', '23级鲤鱼'] }\n]);\n\n\n// 阅读进度和返回顶部逻辑\nconst readingProgress = ref(0);\nlet totalScrollHeight = 0; // 页面总可滚动高度\n\n\n\nonReady(() => {\n\t// 页面渲染完成后，获取内容区域的总高度\n\tuni.createSelectorQuery().select('.page-container').boundingClientRect(pageRect => {\n\t\tif (pageRect) {\n\t\t\t// totalScrollHeight = 实际内容高度 - 视口高度\n\t\t\ttotalScrollHeight = pageRect.height - uni.getSystemInfoSync().windowHeight;\n\t\t\tif (totalScrollHeight < 0) totalScrollHeight = 0; // 防止负值\n\t\t}\n\t}).exec();\n});\n\nonPageScroll((e) => {\n\tif (totalScrollHeight > 0) {\n\t\treadingProgress.value = (e.scrollTop / totalScrollHeight) * 100;\n\t\tif (readingProgress.value > 100) readingProgress.value = 100; // 确保不超过100%\n\t} else {\n\t\treadingProgress.value = 0;\n\t}\n});\n\nconst scrollToTop = () => {\n\tuni.pageScrollTo({\n\t\tscrollTop: 0,\n\t\tduration: 300\n\t});\n};\r\n\r\n\r\n\r\nimport CustomModal from '../common/CustomModal.vue'; \n\nconst isModalShow = ref(false);\n// 内容里直接写 \\n ，配合组件里的 white-space: pre-wrap 即可换行\nconst modalContent = ref(`河南农业大学IT工作室隶属于学校信息化办公室，主要负责校园信息化建设与维护。\n\n招募方向：\n- 前端开发(Web/小程序)\n- 后端开发\n- UI/UX设计\n- 运维技术\n\n有意向的同学请关注\"河南农业大学信息化办公室\"公众号了解详情！`);\n\n// 打开模态框\nconst openModal = () => {\n  isModalShow.value = true;\n};\n\n// 关闭模态框（确认按钮回调）\nconst closeModal = () => {\n  isModalShow.value = false;\n  // 这里可写确认后的逻辑，比如埋点、跳转等\n};\r\n\n\n// 解析答案字符串，将高亮部分分离\nconst parseAnswer = (answerString) => {\n    const parts = [];\n    const regex = /<text class=\"highlight\">(.*?)<\\/text>/g;\n    let lastIndex = 0;\n    let match;\n\n    while ((match = regex.exec(answerString)) !== null) {\n        // Add plain text before the highlight\n        if (match.index > lastIndex) {\n            parts.push({ type: 'text', content: answerString.substring(lastIndex, match.index) });\n        }\n        // Add highlight text\n        parts.push({ type: 'highlight', content: match[1] });\n        lastIndex = regex.lastIndex;\n    }\n\n    // Add any remaining plain text after the last highlight\n    if (lastIndex < answerString.length) {\n        parts.push({ type: 'text', content: answerString.substring(lastIndex) });\n    }\n    return parts;\n};\n\n// 问答数据 (已从模板中提取到 script 部分，便于管理和维护)\nconst registrationQa = ref(qa);\n\n\n\n// 图片预览逻辑已修改为跳转到详情页\nconst previewImage = (imageUrl) => {\n\tuni.navigateTo({\n\t\turl: `/pages/detail/detail?imageUrl=${encodeURIComponent(imageUrl)}`,\n\t});\n};\n\n\n</script>\n\n<style scoped>\n/* 页面容器 */\n.page-container {\n\twidth: 100%;\n\tmin-height: 100vh; /* 确保页面高度至少为视口高度 */\n\tbox-sizing: border-box;\n}\n\n\n.bottom{\r\n\tmargin-left: 90px;\n\tfont-size: 15px;\n\tfont-weight: 1000;\n\ttext-align: center;\n\tcolor: #7a776f;\n\tmargin-top: 50rpx; /* 确保底部有足够空间 */\n\tpadding-bottom: 20rpx; /* 底部填充 */\n}\n\n/* 内容区域包裹器 */\n.content-wrapper {\n\twidth: 90%;\n\tmargin: 0 auto;\n\t\n\tborder-radius: 16rpx; /* 圆角 */\n\t/* 调整内容区域的内边距，左右各增加到30rpx */\n\tpadding: 100rpx 30rpx 30rpx 30rpx; /* 上、右、下、左 */\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05); /* 轻微阴影 */\n}\n\n/* 贡献者按钮已移除样式 */\n/* .contributor-button { ... } */\n\n\n/* 标题样式 */\n.header-section, .contributor-section .section-title {\n\ttext-align: center;\n\tmargin: 0rpx auto 30rpx; /* 上下边距，左右居中 */\n\tborder-bottom: 2rpx solid #338174; /* 底部边框 */\n\tpadding-bottom: 10rpx; /* 边框与文字间距 */\n\tdisplay: table; /* 使边框只包裹内容宽度 */\n}\n.image{\n\twidth: 100%;\n}\n\n.section-title {\n\tfont-size: 33.6rpx;\n\tfont-weight: bold;\n\tcolor: #000000; /* 标题颜色改为深蓝色，与主题色保持一致 */\n\tline-height: 1.75;\n\twhite-space: nowrap; /* 防止标题换行 */\n}\n\n/* 问答项样式 */\n.qa-item {\n\tmargin: 30rpx 0;\n\tline-height: 1.75;\n\tfont-size: 28rpx;\n\tletter-spacing: 0.05em;\n\tcolor: #333333;\n\ttext-align: justify;\n}\n\n.question {\n\tfont-weight: bold;\n\tcolor: #55aa00; /* 绿色问题 */\n\tdisplay: block;\n\tmargin-bottom: 10rpx;\n}\n\n.answer {\n\tdisplay: block;\n}\n\n.highlight {\n\tcolor: #0f4c81; /* 高亮颜色改为深蓝色，与主题色保持一致 */\n\tfont-weight: bold;\n}\n\n\n/* 贡献者名单直接展示的样式 */\n.contributor-section {\n\tmargin-top: 80rpx; /* 与上方内容保持距离 */\n\tpadding-top: 40rpx;\n\tborder-top: 2rpx solid #e0e0e0; /* 顶部加一条分割线 */\n}\n\n.modal-contributors-list { /* 复用原有的列表样式，但现在是直接在页面中 */\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\twidth: 100%;\n}\n\n\n/* 贡献者类别容器 */\n.contributor-category {\n\twidth: 100%;\n\tmargin-bottom: 40rpx; /* 类别之间的间距 */\n}\n\n/* 类别标题 */\n.category-title {\n\tfont-size: 32rpx; /* 类别标题字号 */\n\tfont-weight: bold;\n\tcolor: #609c63; /* 类别标题颜色 */\n\tmargin-bottom: 20rpx; /* 标题与名字的间距 */\n\tdisplay: block; /* 确保标题独占一行 */\n\ttext-align: center;\n\tborder-bottom: 2rpx solid #e0e0e0; /* 底部细线 */\n\tpadding-bottom: 10rpx;\n}\n\n/* 名字网格布局 */\n.names-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap; /* 允许换行 */\n\tjustify-content: center; /* 名字居中对齐 */\n\tgap: 20rpx 30rpx; /* 行间距和列间距 */\n}\n\n.contributor-name {\n\tfont-size: 30rpx; /* 贡献者名字字号稍大 */\n\tcolor: #4a4a4a; /* 更深的灰色 */\n\tline-height: 1.6;\n\tpadding: 5rpx 0; /* 增加垂直内边距 */\n\ttransition: color 0.2s ease;\n}\n\n\n/* 返回顶部按钮和阅读进度样式 */\n.scroll-controls {\n\tposition: fixed;\n\tbottom: 130rpx; /* 距离底部 */\n\tright: 20rpx; /* 距离右侧 */\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tz-index: 99; /* 确保在内容之上 */\n}\n\n.reading-progress {\n\tbackground-color: rgba(0, 0, 0, 0.6);\n\tcolor: #fff;\n\tfont-size: 24rpx;\n\tpadding: 10rpx 20rpx;\n\tborder-radius: 30rpx;\n\tmargin-bottom: 10rpx;\n\twhite-space: nowrap; /* 防止百分比换行 */\n}\n\n.back-to-top-button {\n\tbackground-color: #0f4c81; /* 匹配主题色 */\n\tcolor: #fff;\n\twidth: 80rpx;\n\theight: 80rpx;\n\tborder-radius: 50%; /* 圆形按钮 */\n\tdisplay: flex; /* 使用flexbox来居中图片 */\n\tjustify-content: center;\n\talign-items: center;\n\tbox-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);\n\tborder: none;\n\tpadding: 0; /* 移除默认padding */\n}\n\n.top-arrow-icon {\n\twidth: 80rpx; /* 图片大小 */\n\theight: 80rpx;\n\t/* 可以添加滤镜来改变颜色，如果图片是黑色的 */\n\t/* filter: invert(100%); */\n}\n\n/* 动画效果已移除 (弹窗) */\n/* @keyframes fadeIn { ... } */\n/* @keyframes slideIn { ... } */\n</style>\n", "import MiniProgramPage from 'D:/新生小程序/microportal-dev-ls/pages/NewStudentKnowledge/comment/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onReady", "uni", "onPageScroll"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuGA,MAAA,cAAA,MAAA;;;;AAzCA,UAAA,eAAAA,cAAAA,IAAA;AAAA,MACA,EAAA,UAAA,iBAAA,OAAA,CAAA,YAAA,SAAA,aAAA,SAAA,SAAA,WAAA,SAAA,SAAA,YAAA,EAAA;AAAA,MACA,EAAA,UAAA,SAAA,OAAA,CAAA,UAAA,OAAA,EAAA;AAAA,IACA,CAAA;AAIA,UAAA,kBAAAA,cAAAA,IAAA,CAAA;AACA,QAAA,oBAAA;AAIAC,kBAAAA,QAAA,MAAA;AAEAC,oBAAA,MAAA,oBAAA,EAAA,OAAA,iBAAA,EAAA,mBAAA,cAAA;AACA,YAAA,UAAA;AAEA,8BAAA,SAAA,SAAAA,cAAA,MAAA,kBAAA,EAAA;AACA,cAAA,oBAAA;AAAA,gCAAA;AAAA,QACA;AAAA,MACA,CAAA,EAAA,KAAA;AAAA,IACA,CAAA;AAEAC,kBAAA,aAAA,CAAA,MAAA;AACA,UAAA,oBAAA,GAAA;AACA,wBAAA,QAAA,EAAA,YAAA,oBAAA;AACA,YAAA,gBAAA,QAAA;AAAA,0BAAA,QAAA;AAAA,MACA,OAAA;AACA,wBAAA,QAAA;AAAA,MACA;AAAA,IACA,CAAA;AAEA,UAAA,cAAA,MAAA;AACAD,oBAAAA,MAAA,aAAA;AAAA,QACA,WAAA;AAAA,QACA,UAAA;AAAA,MACA,CAAA;AAAA,IACA;AAMA,UAAA,cAAAF,cAAAA,IAAA,KAAA;AAEA,UAAA,eAAAA,cAAA,IAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCAQA;AAGA,UAAA,YAAA,MAAA;AACA,kBAAA,QAAA;AAAA,IACA;AAGA,UAAA,aAAA,MAAA;AACA,kBAAA,QAAA;AAAA,IAEA;AAIA,UAAA,cAAA,CAAA,iBAAA;AACA,YAAA,QAAA,CAAA;AACA,YAAA,QAAA;AACA,UAAA,YAAA;AACA,UAAA;AAEA,cAAA,QAAA,MAAA,KAAA,YAAA,OAAA,MAAA;AAEA,YAAA,MAAA,QAAA,WAAA;AACA,gBAAA,KAAA,EAAA,MAAA,QAAA,SAAA,aAAA,UAAA,WAAA,MAAA,KAAA,EAAA,CAAA;AAAA,QACA;AAEA,cAAA,KAAA,EAAA,MAAA,aAAA,SAAA,MAAA,CAAA,EAAA,CAAA;AACA,oBAAA,MAAA;AAAA,MACA;AAGA,UAAA,YAAA,aAAA,QAAA;AACA,cAAA,KAAA,EAAA,MAAA,QAAA,SAAA,aAAA,UAAA,SAAA,EAAA,CAAA;AAAA,MACA;AACA,aAAA;AAAA,IACA;AAGA,UAAA,iBAAAA,cAAAA,IAAA,EAAA;AAKA,UAAA,eAAA,CAAA,aAAA;AACAE,oBAAAA,MAAA,WAAA;AAAA,QACA,KAAA,iCAAA,mBAAA,QAAA,CAAA;AAAA,MACA,CAAA;AAAA,IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClKA,GAAG,WAAW,eAAe;"}