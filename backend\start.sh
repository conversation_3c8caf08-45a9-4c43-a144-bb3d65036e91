#!/bin/bash

# 配置应用名称
APP_NAME="NewStudentKnowledge"
APP_ENTRY="app.js"

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "错误: 未检测到Node.js，请先安装Node.js"
    exit 1
fi

# 检查PM2是否安装，未安装则自动安装
if ! command -v pm2 &> /dev/null; then
    echo "未检测到PM2，正在安装..."
    npm install pm2 -g &> /dev/null
    if [ $? -ne 0 ]; then
        echo "PM2安装失败，请手动安装: npm install pm2 -g"
        exit 1
    fi
fi

# 确保配置文件开启静默模式
if [ -f "config/config.json" ]; then
    # 使用jq工具更新配置（如果已安装）
    if command -v jq &> /dev/null; then
        jq '.silentMode = true' config/config.json > config/temp.json
        mv config/temp.json config/config.json
    else
        # 不使用jq的简单替换（适用于基础配置）
        sed -i 's/"silentMode": false/"silentMode": true/' config/config.json 2>/dev/null
        # 如果配置中没有silentMode，则添加它
        if ! grep -q "silentMode" config/config.json; then
            sed -i '$s/}/,"silentMode": true}/' config/config.json
        fi
    fi
else
    echo "警告: 未找到配置文件，将使用默认静默模式"
fi

# 检查应用是否已在运行，如已运行则先停止
pm2 describe $APP_NAME &> /dev/null
if [ $? -eq 0 ]; then
    echo "检测到应用已运行，正在重启..."
    pm2 restart $APP_NAME &> /dev/null
else
    echo "启动应用并在后台静默运行..."
    pm2 start $APP_ENTRY --name $APP_NAME --silent &> /dev/null
fi

# 设置开机自启动
pm2 startup &> /dev/null
pm2 save &> /dev/null

echo "应用已成功在后台静默启动"
echo "应用名称: $APP_NAME"
echo "查看状态: pm2 status $APP_NAME"
echo "停止应用: ./stop.sh"
