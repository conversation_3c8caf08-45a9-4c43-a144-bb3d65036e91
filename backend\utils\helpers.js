/**
 * 获取客户端真实IP地址
 * @param {Object} req - Express请求对象
 * @returns {string} 客户端IP地址
 */
function getClientIP(req) {
  return req.headers['x-forwarded-for'] ||
         req.headers['x-real-ip'] ||
         req.connection.remoteAddress ||
         req.socket.remoteAddress ||
         (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
         req.ip;
}

module.exports = {
  getClientIP
};
