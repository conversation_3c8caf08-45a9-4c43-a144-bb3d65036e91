<template>
  <view class="webview-container">
    <!-- web-view组件用于加载外部网页 -->
    <web-view :src="url" @load="onWebviewLoad" @error="onWebviewError"></web-view>
  </view>
</template>

<script setup>
import {
  onLoad
} from '@dcloudio/uni-app';
import {
  ref
} from 'vue';

const url = ref('');
const loading = ref(true); // 控制加载提示的显示

// 页面加载生命周期钩子，获取传递过来的URL参数
onLoad((options) => {
  if (options.url) {
    // 对URL进行解码
    url.value = decodeURIComponent(options.url);
    console.log('加载外部链接:', url.value);
  } else {
    // 如果没有URL参数，可以显示错误信息或返回
    console.error('未接收到有效的URL参数');
    uni.showToast({
      title: '链接无效',
      icon: 'none'
    });
    loading.value = false; // 停止加载提示
  }
});

// web-view加载成功事件（注意：此事件在小程序中可能不总是触发，或触发时机较晚）
const onWebviewLoad = () => {
  console.log('Webview加载成功');
  loading.value = false; // 隐藏加载提示
};

// web-view加载失败事件
const onWebviewError = (e) => {
  console.error('Webview加载失败:', e.detail);
  uni.showToast({
    title: '链接加载失败，请检查网络或稍后重试',
    icon: 'none',
    duration: 3000
  });
  loading.value = false; // 隐藏加载提示
};

</script>

<style lang="scss" scoped>
.webview-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

web-view {
  flex: 1; // web-view填充剩余空间
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9); // 半透明白色背景
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999; // 确保在web-view之上
}

.loading-text {
  font-size: 32rpx;
  color: #666;
}
</style>
