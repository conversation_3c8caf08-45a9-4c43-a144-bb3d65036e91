<template>
  <view class="container">
    <!-- 卡片列表区域，可垂直滚动 -->
    <scroll-view class="card-list" scroll-y="true">
		
		

		
	
		
      <!-- 卡片1：文化路校区 -->
      <view class="card color-card-1" @click="navigateTo('wenhua')">
        <!-- 背景图片 -->
        <image class="card-image" src="https://itstudio.henau.edu.cn/image_hosting/uploads/68945f019f2b3_1754554113.png" mode="aspectFill"></image>
        <!-- 图片上的半透明覆盖层，用于提高文字对比度 -->
        <view class="card-overlay"></view>
        <!-- 卡片内容区域 -->
        <view class="card-content">
          <view class="card-text-container">
            <text class="card-title">文化路校区</text>
            <text class="card-subtitle">历史底蕴，学术殿堂</text>
          </view>
          <!-- 箭头文本 -->
          <text class="card-arrow-text">></text>
        </view>
      </view>

      <!-- 卡片2：龙子湖校区 -->
      <view class="card color-card-2" @click="navigateTo('longzihu')">
        <!-- 背景图片 -->
        <image class="card-image" src="https://itstudio.henau.edu.cn/image_hosting/uploads/68945ea026d3f_1754554016.png" mode="aspectFill"></image>
        <!-- 图片上的半透明覆盖层，用于提高文字对比度 -->
        <view class="card-overlay"></view>
        <!-- 卡片内容区域 -->
        <view class="card-content">
          <view class="card-text-container">
            <text class="card-title">龙子湖校区</text>
            <text class="card-subtitle">现代气息，活力新城</text>
          </view>
          <!-- 箭头文本 -->
          <text class="card-arrow-text">></text>
        </view>
      </view>
	  
	  
	  
	  <!-- 卡片3：许昌校区，纯灰色背景（无图片） -->
	  <view class="card grey-card" @click="navigateTo('xuchang')">
	  		          <image class="card-image" src="https://itstudio.henau.edu.cn/image_hosting/uploads/68945d60691e0_1754553696.png" mode="aspectFill"></image>
	    <!-- 灰色卡片没有背景图片和覆盖层 -->
	    <view class="card-content" @click="jumpxuchang()">
	      <view class="card-text-container">
	       <text class="card-title">许昌校区</text>
	       <text class="card-subtitle">星辰大海,等你征服</text>
	      </view>
	      <!-- 箭头文本 -->
	      <text class="card-arrow-text">></text>
	    </view>
	  </view>
	  
	  
	  
	  


	  
	  
	  <!-- 卡片4：常用app -->
	  <view class="card color-card-2" @click="navigateTo('changyong')">
	    <!-- 背景图片 -->
	    <image class="card-image" src="https://itstudio.henau.edu.cn/image_hosting/uploads/689494fb37ec1_1754567931.png" mode="aspectFill"></image>
	    <!-- 图片上的半透明覆盖层，用于提高文字对比度 -->
	    <view class="card-overlay"></view>
	    <!-- 卡片内容区域 -->
	    <view class="card-content">
	      <view class="card-text-container">
	        <text class="card-title">常用软件</text>
	        <text class="card-subtitle">校园必备，尽在掌中</text>
	      </view>
	      <!-- 箭头文本 -->
	      <text class="card-arrow-text">></text>
	    </view>
	  </view>
  <text class="bottom" @click="openModal">河南农业大学IT工作室(欢迎加入)</text>
    <!-- 引入自定义模态框组件 -->
  <CustomModal 
      :visible="isModalShow" 
      title="河南农业大学IT工作室" 
      :content="modalContent" 
      confirmText="好的" 
      :showCancel="false" 
      @confirm="closeModal"
    />	
    </scroll-view>

  </view>
</template>

<script setup>
	import { ref } from 'vue';
// 导航到指定校区的方法
const navigateTo = (campus) => {
  console.log(`导航到 ${campus} 校区`);
  // 实际页面跳转（根据需要取消注释并确认路径正确性）
  uni.navigateTo({
    url: `/pages/NewStudentKnowledge/${campus}/${campus}`
  });
};

// 引入组件（路径按实际调整，比如 components/CustomModal.vue）
import CustomModal from '../common/CustomModal.vue'; 

const isModalShow = ref(false);
const modalContent = ref(`河南农业大学IT工作室隶属于河南农业大学信息化办公室，主要负责校园信息化建设与维护。
-----------主要职务内容-------------
前端开发(Web/小程序)
后端开发
UI/UX设计
运维技术
-----------------------------------
有意向的同学请关注
"河南农业大学信息化办公室"公众号
我们期待你的加入！`);

// 打开模态框
const openModal = () => {
  isModalShow.value = true;
};

// 关闭模态框（确认按钮回调）
const closeModal = () => {
  isModalShow.value = false;
  // 这里可写确认后的逻辑，比如埋点、跳转等
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh; /* 容器高度占满整个视口 */
  background-color: #f7f7f7; /* 页面背景色 */
}

// 顶部状态栏占位，用于适配刘海屏等设备
.header-status-bar {
  height: var(--status-bar-height); /* uni-app 提供的状态栏高度变量 */
  width: 100%;
  background-color: #ffffff;
}

.header {
  padding: 10px;
  background-color: #ffffff;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05); /* 底部阴影效果 */
}
.bottom{
	margin-top: 90px;
	margin-left: 60px;
	font-size: 15px;
	font-weight: 1000;
	text-align: center;
	color: #7a776f;
}
.header-title {
  font-size: 28px;
  font-weight: bold;
  color: #333333;
}

.header-subtitle {
  font-size: 14px;
  color: #999999;
}

.card-list {
  flex: 1; /* 占据剩余垂直空间 */
  padding: 10px; /* 列表内边距，提供左右留白 */
  overflow-y: auto; /* 允许垂直滚动 */
  
  // ===================== 卡片宽度控制区域 =====================
  // 这里的 max-width 控制了卡片列表在宽屏设备上的最大宽度
  // 调整这个值即可改变卡片的宽度，例如 350px
  max-width: 350px;
  margin: 0 auto; /* 左右自动外边距，实现居中 */
  // ========================================================
}

.card {
  position: relative; /* 相对定位，用于子元素的绝对定位 */
  height: 200px;
  margin-bottom: 5px; /* 卡片之间的间距 */
  border-radius: 15px;
  overflow: hidden; /* 隐藏超出圆角的部分 */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* 卡片阴影 */
  display: flex; /* 使用 flex 布局 */
  align-items: flex-end; /* 内容垂直对齐到底部 */
  
  transition: transform 0.2s ease-in-out; /* 点击时的动画效果 */
  &:active {
    transform: scale(0.98); /* 点击时轻微缩小 */
  }
}

// 卡片背景图片样式
.card-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1; /* 位于最底层 */
  object-fit: cover; /* 保持图片比例并填充容器 */
}

// 图片上的半透明覆盖层，用于增强文字对比度
.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent); /* 从下到上的黑色渐变 */
  z-index: 2; /* 位于图片之上，内容之下 */
}

// 纯色卡片背景（当没有图片时使用）
.color-card-1 {
	margin-top: 5px;
  background-color: #8c738e; /* 紫色系 */
}

.color-card-2 {
  background-color: #4a90e2; /* 蓝色系 */
}

// 灰色卡片（许昌校区）
.grey-card {
  background-color: #e0e0e0;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05); /* 较浅的阴影 */
}

.card-content {
  width: 100%;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 3; /* 确保内容在最上层 */
  color: #ffffff; /* 默认文字颜色为白色 */

  // 灰色卡片中的文字颜色为深色
  .grey-card & {
    color: #333333;
  }
}

.card-text-container {
	
  display: flex;
  flex-direction: column;
}

.card-title {
	color: white;
  font-size: 20px;
  font-weight: bold;
}

.card-subtitle {
	color: white;
  font-size: 14px;
  margin-top: 5px;

  // 灰色卡片中的副标题颜色
  .grey-card & {
  }
}

.card-arrow-text {
  font-size: 24px;
  font-weight: bold;
  opacity: 0.6; /* 增加透明度，看起来更柔和 */
}

// 灰色卡片中的箭头文本颜色
.grey-card .card-arrow-text {
  color: #666666;
}
</style>
