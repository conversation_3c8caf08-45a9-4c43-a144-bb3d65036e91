{"version": 3, "file": "test.js", "sources": ["pages/NewStudentKnowledge/test/test.vue", "../../test/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvTmV3U3R1ZGVudEtub3dsZWRnZS90ZXN0L3Rlc3QudnVl"], "sourcesContent": ["<template>\n  <text class=\"open-btn bottom\" @click=\"openModal\">河南农业大学IT工作室(欢迎加入)</text>\n    <!-- 引入自定义模态框组件 -->\n  <CustomModal \n      :visible=\"isModalShow\" \n      title=\"河南农业大学IT工作室\" \n      :content=\"modalContent\" \n      confirmText=\"好的\" \n      :showCancel=\"false\" \n      @confirm=\"closeModal\"\n    />\t\t\n</template>\n\n<script setup>\nimport { ref } from 'vue';\n// 引入组件（路径按实际调整，比如 components/CustomModal.vue）\nimport CustomModal from '../common/CustomModal.vue'; \n\nconst isModalShow = ref(false);\n// 内容里直接写 \\n ，配合组件里的 white-space: pre-wrap 即可换行\nconst modalContent = ref(`河南农业大学IT工作室隶属于学校信息化办公室，主要负责校园信息化建设与维护。\n\n招募方向：\n- 前端开发(Web/小程序)\n- 后端开发\n- UI/UX设计\n- 运维技术\n\n有意向的同学请关注\"河南农业大学信息化办公室\"公众号了解详情！`);\n\n// 打开模态框\nconst openModal = () => {\n  isModalShow.value = true;\n};\n\n// 关闭模态框（确认按钮回调）\nconst closeModal = () => {\n  isModalShow.value = false;\n  // 这里可写确认后的逻辑，比如埋点、跳转等\n};\n</script>\n\n", "import MiniProgramPage from 'D:/新生小程序/microportal-dev-ls/pages/NewStudentKnowledge/test/test.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "MiniProgramPage"], "mappings": ";;;;;AAgBA,MAAM,cAAc,MAAW;;;;AAE/B,UAAM,cAAcA,cAAAA,IAAI,KAAK;AAE7B,UAAM,eAAeA,cAAG,IAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCAQO;AAGhC,UAAM,YAAY,MAAM;AACtB,kBAAY,QAAQ;AAAA,IACtB;AAGA,UAAM,aAAa,MAAM;AACvB,kBAAY,QAAQ;AAAA,IAEtB;;;;;;;;;;;;;;;;ACtCA,GAAG,WAAWC,SAAe;"}