"use strict";
const common_vendor = require("../../common/vendor.js");
const stores_base = require("../../stores/base.js");
const _sfc_main = {
  __name: "index",
  setup(__props) {
    stores_base.useBaseStore();
    const servicesData = common_vendor.ref([
      {
        service_module_id: 1,
        service_module_name: "综合服务",
        service_id: 2,
        service_name: "本科生请假",
        service_type: "H5APP",
        service_icon: "/static/icon/GeneralService/UndergraduateLeave.png",
        service_url: "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxf40cdbdcc58c583e&redirect_uri=https%3a%2f%2fstudqj.henau.edu.cn&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect",
        service_order: 100,
        service_module_order: 1
      },
      {
        service_module_id: 1,
        service_module_name: "综合服务",
        service_id: 4,
        service_name: "实验室管理",
        service_type: "H5APP",
        service_icon: "/static/icon/GeneralService/Laboratory.png",
        service_url: "https://oauth.henau.edu.cn/oauth2_qr_connect/login?appid=nd35b284cce2d94f0d&redirect_uri=https%3a%2f%2fsysxxh.henau.edu.cn%2foauthlogin.aspx&response_type=code&scope=henauapi_login&state=app",
        service_order: 100,
        service_module_order: 1
      },
      {
        service_module_id: 1,
        service_module_name: "综合服务",
        service_id: 37,
        service_name: "校园服务电话",
        service_type: "H5APP",
        service_icon: "/static/icon/GeneralService/Telephone.png",
        service_url: "https://microservices.henau.edu.cn/henauwfw/#/Telephone",
        service_order: 100,
        service_module_order: 1
      },
      {
        service_module_id: 1,
        service_module_name: "综合服务",
        service_id: 43,
        service_name: "校内业务用表",
        service_type: "H5APP",
        service_icon: "/static/icon/GeneralService/BusinessForm.png",
        service_url: "https://microservices.henau.edu.cn/henauwfw/#/BusinessForm",
        service_order: 100,
        service_module_order: 1
      },
      {
        service_module_id: 5,
        service_module_name: "建议反馈",
        service_id: 34,
        service_name: "灵感小站",
        service_type: "H5APP",
        service_icon: "/static/icon/SuggestionFeedback/InspirationStation.png",
        service_url: "https://microservices.henau.edu.cn/henauwfw/#/SubmitFeedback",
        service_order: 100,
        service_module_order: 5
      },
      {
        service_module_id: 1,
        service_module_name: "综合服务",
        service_id: 57,
        service_name: "VPN登录",
        service_type: "H5APP",
        service_icon: "/static/icon/GeneralService/VPNLogin.png",
        service_url: "https://vpn2.henau.edu.cn/portal/",
        service_order: 100,
        service_module_order: 1
      },
      {
        service_module_id: 4,
        service_module_name: "我的信息",
        service_id: 38,
        service_name: "我的消息",
        service_type: "H5APP",
        service_icon: "/static/icon/GeneralService/MyMessage.png",
        service_url: "https://microservices.henau.edu.cn/henauwfw/#/MyMessage",
        service_order: 100,
        service_module_order: 4
      },
      // {
      // 	service_module_id: 3,
      // 	service_module_name: "学习服务",
      // 	service_id: 25,
      // 	service_name: "课表查询",
      // 	service_type: "WXAPP",
      // 	service_icon: "/static/icon/LearningService/ScheduleInquiry.png",
      // 	service_url: "",
      // 	wxapp_id: "wx5da532e7f5b2afaf",
      // 	service_order: 100,
      // 	service_module_order: 3,
      // },
      {
        service_module_id: 3,
        service_module_name: "综合服务",
        service_id: 18,
        service_name: "校园网注册",
        service_type: "H5APP",
        service_icon: "/static/icon/ResourceApply/CampusNetwork.png",
        service_url: "https://oauth.henau.edu.cn/app/CNPRS",
        service_order: 100,
        service_module_order: 3
      },
      {
        service_module_id: 3,
        service_module_name: "学习服务",
        service_id: 30,
        service_name: "电子成绩单",
        service_type: "WXAPP",
        service_icon: "/static/icon/LearningService/Transcript.png",
        service_url: "",
        wxapp_id: "wx5da532e7f5b2afaf",
        service_order: 100,
        service_module_order: 3
      },
      {
        service_module_id: 1,
        service_module_name: "综合服务",
        service_id: 45,
        service_name: "校园地图",
        service_type: "H5APP",
        service_icon: "/static/icon/GeneralService/henaumap.svg",
        service_url: "https://henaumap.henau.edu.cn/",
        service_order: 100,
        service_module_order: 1
      },
      {
        service_module_id: 1,
        service_module_name: "综合服务",
        service_id: 58,
        service_name: "OA办公系统",
        service_type: "H5APP",
        service_icon: "/static/icon/GeneralService/OA.png",
        service_url: "https://oauth.henau.edu.cn/oauth2_qr_connect/login?appid=ndfe16be378bbef3a1&redirect_uri=https%3A%2F%2Foa.henau.edu.cn/sso.php&response_type=code&scope=henauapi_login&state=1_",
        service_order: 100,
        service_module_order: 1
      }
    ]);
    const widgetData = common_vendor.ref([
      {
        service_id: 1,
        service_name: "校园卡付款码",
        service_type: "H5APP",
        service_icon: "/static/icon/GeneralService/CampusCard.png",
        service_url: "https://yktwx.henau.edu.cn/berserker-auth/wechat/token/mp?resultUrl=https%3A%2F%2Fyktwx.henau.edu.cn%2Fplat%2Fpay%3FappId%3D12%26loginFrom%3Dwechat-mp%26synAccessSource%3Dwechat-mp%26nodeId%3D-12"
      },
      // {
      // 	service_id: 2,
      // 	service_name: "校园网注册",
      // 	service_type: "H5APP",
      // 	service_icon: "/static/icon/ResourceApply/CampusNetwork.png",
      // 	service_url: "https://oauth.henau.edu.cn/app/CNPRS",
      // },
      {
        service_id: 2,
        service_name: "访客预约",
        service_type: "H5APP",
        service_icon: "/static/icon/GeneralService/Visitor.png",
        service_url: "https://bwcfr.henau.edu.cn/visitor/#/pages/index/index"
      },
      {
        service_id: 3,
        service_name: "失物招领",
        service_type: "H5APP",
        service_icon: "/static/icon/GeneralService/LosingStuff.svg",
        service_url: "https://swzl.henau.edu.cn/swzl/feed/index"
      },
      {
        service_id: 4,
        service_name: "农宝圈",
        service_type: "H5APP",
        service_icon: "/static/icon/GeneralService/HenauMoments.png",
        service_url: "https://moments.henau.edu.cn/#/Index"
      }
    ]);
    const goToNewStudentCorner = () => {
      common_vendor.index.switchTab({
        url: "/pages/NewStudentKnowledge/index/index"
      });
    };
    const goToService = (item) => {
      if (item.service_type === "H5APP") {
        common_vendor.index.navigateTo({
          url: `/pages/serviceWebView/serviceWebView?webviewUrl=${encodeURIComponent(
            item.service_url
          )}`
        });
      } else if (item.service_type === "WXAPP") {
        common_vendor.index.navigateToMiniProgram({
          appId: item.wxapp_id,
          path: item.service_url,
          success(res) {
            common_vendor.index.__f__("log", "at pages/index/index.vue:290", "小程序跳转成功", res);
          },
          fail(err) {
            common_vendor.index.__f__("log", "at pages/index/index.vue:293", "小程序跳转失败", err);
          }
        });
      } else {
        common_vendor.index.__f__("log", "at pages/index/index.vue:297", "无法识别的服务类型");
      }
    };
    const moreService = () => {
      common_vendor.index.navigateTo({
        url: "/pages/microservices/microservices"
      });
    };
    common_vendor.onShareAppMessage(() => {
    });
    common_vendor.onShareTimeline(() => {
    });
    common_vendor.onLoad((query) => {
      const webViewUrl = decodeURIComponent(query.webViewUrl);
      const q = decodeURIComponent(query.q);
      parseInt(query.scancode_time);
      if (q && q != void 0 && q != "undefined") {
        const acDomains = ["ac.henau.edu.cn"];
        const containsAllowedDomain = (url) => {
          return acDomains.some((domain) => url.includes(domain));
        };
        if (containsAllowedDomain(q)) {
          common_vendor.index.navigateTo({
            url: `/pages/ac/ac?webviewUrl=${encodeURIComponent(q)}`
          });
        } else {
          common_vendor.index.navigateTo({
            url: `/pages/serviceWebView/serviceWebView?webviewUrl=${encodeURIComponent(
              q
            )}`
          });
        }
      }
      if (webViewUrl && webViewUrl !== "undefined") {
        common_vendor.index.navigateTo({
          url: `/pages/serviceWebView/serviceWebView?webviewUrl=${encodeURIComponent(
            webViewUrl
          )}`
        });
      }
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => goToNewStudentCorner()),
        b: common_vendor.f(widgetData.value, (item, index, i0) => {
          return {
            a: item.service_icon || "/static/logo.png",
            b: common_vendor.t(item.service_name),
            c: index,
            d: common_vendor.o(($event) => goToService(item), index)
          };
        }),
        c: common_vendor.f(servicesData.value, (item, index, i0) => {
          return {
            a: item.service_icon || "/static/logo.png",
            b: common_vendor.t(item.service_name),
            c: index,
            d: common_vendor.o(($event) => goToService(item), index)
          };
        }),
        d: common_vendor.o(($event) => moreService())
      };
    };
  }
};
_sfc_main.__runtimeHooks = 6;
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
